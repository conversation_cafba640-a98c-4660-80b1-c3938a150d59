<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Models\Report;

class ApiReportController extends Controller
{
     public function store(Request $request)
    {
        if (!Auth::guard('account')->check()) {
            return response()->json(['message' => 'Chưa đăng nhập'], 401);
        }

        $validator = Validator::make($request->all(), [
            'image' => 'required|image|max:4096',
            'description' => 'required|string',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'gmap_link' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['message' => 'Dữ liệu không hợp lệ', 'errors' => $validator->errors()], 422);
        }

        // <PERSON><PERSON> lý upload ảnh
        $imagePath = $request->file('image')->store('reports', 'public');

        $report = Report::create([
            'created_by' => Auth::guard('account')->id(),
            'image' => $imagePath,
            'description' => $request->description,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'gmap_link' => $request->gmap_link,
            'status' => 'pending',
        ]);

        return response()->json(['message' => 'Báo cáo đã được gửi thành công!', 'redirect' => url('/map')], 201);
    }

    /**
     * @OA\Post(
     *     path="/api/app/reports/create-report",
     *     tags={"Reports"},
     *     summary="Gửi báo cáo điểm rác (app)",
     *     description="Gửi báo cáo điểm rác với hình ảnh, mô tả, tọa độ GPS (dành cho mobile app)",
     *     security={{"sanctum": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 required={"image", "description", "latitude", "longitude"},
     *                 @OA\Property(property="image", type="string", format="binary", description="Hình ảnh điểm rác"),
     *                 @OA\Property(property="description", type="string", example="Rác thải tại công viên", description="Mô tả chi tiết"),
     *                 @OA\Property(property="latitude", type="number", format="float", example=21.0285, description="Vĩ độ GPS"),
     *                 @OA\Property(property="longitude", type="number", format="float", example=105.8542, description="Kinh độ GPS"),
     *                 @OA\Property(property="gmap_link", type="string", example="https://maps.google.com/...", description="Link Google Maps (tùy chọn)")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Báo cáo được gửi thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Báo cáo đã được gửi thành công!"),
     *             @OA\Property(property="report_id", type="integer", example=123)
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Chưa đăng nhập",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Bạn chưa đăng nhập!")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Dữ liệu không hợp lệ",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Dữ liệu không hợp lệ"),
     *             @OA\Property(property="errors", type="object")
     *         )
     *     )
     * )
     */
    public function createReport(Request $request)
    {
        $user = $request->user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Bạn chưa đăng nhập!'
            ], 401);
        }
        $validator = Validator::make($request->all(), [
            'image' => 'required|image|max:4096',
            'description' => 'required|string',
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'gmap_link' => 'nullable|string',
        ]);
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Dữ liệu không hợp lệ',
                'errors' => $validator->errors()
            ], 422);
        }
        $imagePath = $request->file('image')->store('reports', 'public');
        $report = \App\Models\Report::create([
            'created_by' => $user->id,
            'image' => $imagePath,
            'description' => $request->description,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'gmap_link' => $request->gmap_link,
            'status' => 'pending',
        ]);
        return response()->json([
            'success' => true,
            'message' => 'Báo cáo đã được gửi thành công!',
            'report_id' => $report->id
        ], 201);
    }

    /**
     * @OA\Post(
     *     path="/api/app/reports/history",
     *     tags={"Reports"},
     *     summary="Lịch sử báo cáo rác",
     *     description="Lấy lịch sử các báo cáo rác của người dùng (yêu cầu đăng nhập, có phân trang)",
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Số trang",
     *         required=false,
     *         @OA\Schema(type="integer", default=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Số bản ghi mỗi trang (tối đa 50)",
     *         required=false,
     *         @OA\Schema(type="integer", default=15, maximum=50)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Lấy lịch sử thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="array",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="description", type="string", example="Rác thải tại công viên"),
     *                     @OA\Property(property="image", type="string", example="http://example.com/storage/reports/image.jpg"),
     *                     @OA\Property(property="latitude", type="number", format="float", example=21.0285),
     *                     @OA\Property(property="longitude", type="number", format="float", example=105.8542),
     *                     @OA\Property(property="gmap_link", type="string", example="https://maps.google.com/...", nullable=true),
     *                     @OA\Property(property="status", type="string", example="pending"),
     *                     @OA\Property(property="created_at", type="string", example="2024-12-20 10:30:45")
     *                 )
     *             ),
     *             @OA\Property(property="pagination", type="object",
     *                 @OA\Property(property="current_page", type="integer", example=1),
     *                 @OA\Property(property="per_page", type="integer", example=15),
     *                 @OA\Property(property="total", type="integer", example=42),
     *                 @OA\Property(property="last_page", type="integer", example=3),
     *                 @OA\Property(property="has_more", type="boolean", example=true)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Chưa đăng nhập",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Chưa đăng nhập")
     *         )
     *     )
     * )
     */
    public function history(Request $request)
    {
        $user = $request->user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Chưa đăng nhập'
            ], 401);
        }
        $perPage = min((int) $request->get('per_page', 15), 50);
        $reports = \App\Models\Report::where('created_by', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
        $results = $reports->getCollection()->map(function($item) {
            return [
                'id' => $item->id,
                'description' => $item->description,
                'image' => $item->image ? asset($item->image) : null,
                'latitude' => $item->latitude,
                'longitude' => $item->longitude,
                'gmap_link' => $item->gmap_link,
                'status' => $item->status,
                'created_at' => $item->created_at ? formatDateTime($item->created_at) : null,
                'updated_at' => $item->updated_at ? formatDateTime($item->updated_at) : null,
            ];
        });
        return response()->json([
            'success' => true,
            'data' => $results,
            'pagination' => [
                'current_page' => $reports->currentPage(),
                'per_page' => $reports->perPage(),
                'total' => $reports->total(),
                'last_page' => $reports->lastPage(),
                'has_more' => $reports->hasMorePages(),
            ]
        ]);
    }

    /**
     * @OA\Post(
     *     path="/api/app/reports/stats",
     *     tags={"Reports"},
     *     summary="Thống kê báo cáo và đổi quà của user",
     *     description="Trả về các chỉ số: tổng báo cáo, báo cáo đã duyệt, đang chờ, bị từ chối, tổng lần đổi quà, đổi quà đã duyệt, đã hoàn thành, tổng điểm đã tích.",
     *     security={{"sanctum": {}}},
     *     @OA\Response(
     *         response=200,
     *         description="Thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="total_reports", type="integer", example=20),
     *                 @OA\Property(property="approved_reports", type="integer", example=10),
     *                 @OA\Property(property="pending_reports", type="integer", example=5),
     *                 @OA\Property(property="rejected_reports", type="integer", example=5),
     *                 @OA\Property(property="total_gift_redemptions", type="integer", example=8),
     *                 @OA\Property(property="approved_gift_redemptions", type="integer", example=3),
     *                 @OA\Property(property="completed_gift_redemptions", type="integer", example=2),
     *                 @OA\Property(property="total_points_earned", type="integer", example=500)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Chưa đăng nhập",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Chưa đăng nhập")
     *         )
     *     )
     * )
     */
    public function stats(Request $request)
    {
        $user = $request->user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Chưa đăng nhập'
            ], 401);
        }
        // Báo cáo
        $totalReports = \App\Models\Report::where('created_by', $user->id)->count();
        $approvedReports = \App\Models\Report::where('created_by', $user->id)->where('status', 'approved')->count();
        $pendingReports = \App\Models\Report::where('created_by', $user->id)->where('status', 'pending')->count();
        $rejectedReports = \App\Models\Report::where('created_by', $user->id)->where('status', 'rejected')->count();
        // Đổi quà
        $totalGiftRedemptions = \App\Models\GiftRedemption::where('account_id', $user->id)->count();
        $approvedGiftRedemptions = \App\Models\GiftRedemption::where('account_id', $user->id)->where('status', 'approved')->count();
        $completedGiftRedemptions = \App\Models\GiftRedemption::where('account_id', $user->id)->where('status', 'completed')->count();
        // Tổng điểm đã tích
        $totalPointsEarned = \App\Models\PointHistory::where('account_id', $user->id)->where('change', '>', 0)->sum('change');
        return response()->json([
            'success' => true,
            'data' => [
                'total_reports' => $totalReports,
                'approved_reports' => $approvedReports,
                'pending_reports' => $pendingReports,
                'rejected_reports' => $rejectedReports,
                'total_gift_redemptions' => $totalGiftRedemptions,
                'approved_gift_redemptions' => $approvedGiftRedemptions,
                'completed_gift_redemptions' => $completedGiftRedemptions,
                'total_points_earned' => $totalPointsEarned,
            ]
        ]);
    }
} 