<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Gift;

class ApiGiftController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/app/gifts",
     *     tags={"Gifts"},
     *     summary="L<PERSON>y danh sách các món quà",
     *     description="Lấy danh sách các món quà có thể đổi, chỉ lấy quà đang hoạt động.",
     *     @OA\Response(
     *         response=200,
     *         description="Thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="array",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="Túi tote canvas"),
     *                     @OA\Property(property="description", type="string", example="Túi vải thân thiện môi trường"),
     *                     @OA\Property(property="image", type="string", example="http://example.com/storage/gifts/tote.jpg"),
     *                     @OA\Property(property="value", type="integer", example=100),
     *                     @OA\Property(property="quantity", type="integer", example=10),
     *                     @OA\Property(property="total_quantity", type="integer", example=100),
     *                     @OA\Property(property="is_active", type="boolean", example=true),
     *                     @OA\Property(property="available_from", type="string", example="2024-07-01 00:00:00"),
     *                     @OA\Property(property="available_until", type="string", example="2024-12-31 23:59:59")
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function index()
    {
        $gifts = Gift::where('is_active', 1)
            ->orderBy('available_from', 'asc')
            ->get();
        $data = $gifts->map(function($gift) {
            return [
                'id' => $gift->id,
                'name' => $gift->name,
                'description' => $gift->description,
                'image' => $gift->image ? asset($gift->image) : null,
                'value' => $gift->value,
                'quantity' => $gift->quantity,
                'total_quantity' => $gift->total_quantity,
                'is_active' => $gift->is_active,
                'available_from' => $gift->available_from ? formatDateTime($gift->available_from) : null,
                'available_until' => $gift->available_until ? formatDateTime($gift->available_until) : null,
            ];
        });
        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * @OA\Post(
     *     path="/api/app/gifts/redeem",
     *     tags={"Gifts"},
     *     summary="Đổi quà tặng",
     *     description="Sử dụng điểm để đổi quà tặng (yêu cầu đăng nhập)",
     *     security={{"sanctum": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"gift_id", "quantity"},
     *             @OA\Property(property="gift_id", type="integer", example=1, description="ID của quà tặng"),
     *             @OA\Property(property="quantity", type="integer", example=1, minimum=1, maximum=10, description="Số lượng")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Đổi quà thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Đổi quà thành công! Vui lòng chờ admin xác nhận."),
     *             @OA\Property(property="remaining_points", type="integer", example=150),
     *             @OA\Property(property="gift_name", type="string", example="Túi tote canvas"),
     *             @OA\Property(property="quantity", type="integer", example=1),
     *             @OA\Property(property="points_used", type="integer", example=100)
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Không thể đổi quà",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Điểm tích lũy không đủ để đổi quà này!")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Chưa đăng nhập",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Vui lòng đăng nhập để đổi quà!")
     *         )
     *     )
     * )
     */
    public function redeem(Request $request)
    {
        $request->validate([
            'gift_id' => 'required|exists:gifts,id',
            'quantity' => 'required|integer|min:1|max:10',
        ]);

        $user = $request->user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Vui lòng đăng nhập để đổi quà!'
            ], 401);
        }

        $gift = Gift::where('is_active', 1)->findOrFail($request->gift_id);
        if (!$gift->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Quà tặng này hiện không khả dụng!'
            ], 400);
        }
        $now = now();
        if ($gift->available_from > $now || $gift->available_until < $now) {
            return response()->json([
                'success' => false,
                'message' => 'Quà tặng này chưa hoặc đã hết hạn!'
            ], 400);
        }
        if ($gift->quantity < $request->quantity) {
            return response()->json([
                'success' => false,
                'message' => 'Số lượng quà tặng không đủ!'
            ], 400);
        }
        $totalPointsNeeded = $gift->value * $request->quantity;
        if ($user->point < $totalPointsNeeded) {
            return response()->json([
                'success' => false,
                'message' => 'Điểm tích lũy không đủ để đổi quà này!'
            ], 400);
        }
        // Trừ điểm
        $user->point -= $totalPointsNeeded;
        $user->save();
        // Giảm số lượng quà
        $gift->quantity -= $request->quantity;
        $gift->save();
        // Lưu lịch sử đổi quà
        \App\Models\GiftRedemption::create([
            'account_id' => $user->id,
            'gift_id' => $gift->id,
            'quantity' => $request->quantity,
            'point_used' => $totalPointsNeeded,
            'status' => 'pending',
        ]);
        // Lưu lịch sử điểm
        \App\Models\PointHistory::create([
            'account_id' => $user->id,
            'change' => -$totalPointsNeeded,
            'description' => "Đổi quà: {$gift->name} (x{$request->quantity})",
        ]);
        return response()->json([
            'success' => true,
            'message' => 'Đổi quà thành công! Vui lòng chờ admin xác nhận.',
            'remaining_points' => $user->point,
            'gift_name' => $gift->name,
            'quantity' => $request->quantity,
            'points_used' => $totalPointsNeeded,
        ]);
    }

    /**
     * @OA\Post(
     *     path="/api/app/gifts/history",
     *     tags={"Gifts"},
     *     summary="Lịch sử đổi quà",
     *     description="Xem lịch sử đổi quà của người dùng (yêu cầu đăng nhập, có phân trang)",
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Số trang",
     *         required=false,
     *         @OA\Schema(type="integer", default=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Số bản ghi mỗi trang (tối đa 50)",
     *         required=false,
     *         @OA\Schema(type="integer", default=15, maximum=50)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Lấy lịch sử thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="array",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="gift_name", type="string", example="Túi tote canvas"),
     *                     @OA\Property(property="quantity", type="integer", example=1),
     *                     @OA\Property(property="point_used", type="integer", example=100),
     *                     @OA\Property(property="status", type="string", example="pending"),
     *                     @OA\Property(property="created_at", type="string", example="2024-12-20 10:30:45"),
     *                     @OA\Property(property="notes", type="string", nullable=true, example="Ghi chú từ admin")
     *                 )
     *             ),
     *             @OA\Property(property="pagination", type="object",
     *                 @OA\Property(property="current_page", type="integer", example=1),
     *                 @OA\Property(property="per_page", type="integer", example=15),
     *                 @OA\Property(property="total", type="integer", example=42),
     *                 @OA\Property(property="last_page", type="integer", example=3),
     *                 @OA\Property(property="has_more", type="boolean", example=true)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Chưa đăng nhập",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Chưa đăng nhập")
     *         )
     *     )
     * )
     */
    public function history(Request $request)
    {
        $user = $request->user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Chưa đăng nhập'
            ], 401);
        }
        $perPage = min((int) $request->get('per_page', 15), 50);
        $redemptions = \App\Models\GiftRedemption::with('gift')
            ->where('account_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
     
        $results = $redemptions->getCollection()->map(function($item) {
            return [
                'id' => $item->id,
                'gift_name' => $item->gift_name,
                'quantity' => $item->quantity,
                'point_used' => $item->point_used,
                'status' => $item->status,
                'created_at' => $item->created_at ? formatDateTime($item->created_at) : null,
                'notes' => $item->notes ?? null,
            ];
        });
        return response()->json([
            'success' => true,
            'data' => $results,
            'pagination' => [
                'current_page' => $redemptions->currentPage(),
                'per_page' => $redemptions->perPage(),
                'total' => $redemptions->total(),
                'last_page' => $redemptions->lastPage(),
                'has_more' => $redemptions->hasMorePages(),
            ]
        ]);
    }
} 