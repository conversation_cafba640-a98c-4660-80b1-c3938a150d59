<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Report;

class ApiMapController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/app/map",
     *     tags={"Map"},
     *     summary="L<PERSON>y danh sách điểm rác trên bản đồ",
     *     description="Lấy tất cả các điểm báo cáo rác để hiển thị trên bản đồ",
     *     @OA\Response(
     *         response=200,
     *         description="Thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="array",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="description", type="string", example="<PERSON><PERSON>c thải tại công viên"),
     *                     @OA\Property(property="latitude", type="number", format="float", example=21.0285),
     *                     @OA\Property(property="longitude", type="number", format="float", example=105.8542),
     *                     @OA\Property(property="image", type="string", example="http://example.com/storage/reports/image.jpg"),
     *                     @OA\Property(property="status", type="string", example="pending"),
     *                     @OA\Property(property="created_at", type="string", example="2025-07-25 03:26:12")
     *                 )
     *             ),
     *             @OA\Property(property="message", type="string", example="Dữ liệu bản đồ được lấy thành công")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Lỗi server",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Có lỗi xảy ra")
     *         )
     *     )
     * )
     */
    public function appMap(Request $request)
    {
        $reports = Report::where('status', 'approved')->select([
            'id',
            'description',
            'image',
            'latitude',
            'longitude',
            'gmap_link',
            'status',
            'created_at'
        ])->orderByDesc('id')->get();

        $reportsFormatted = $reports->map(function($report) {
            $arr = $report->toArray();
            $arr['created_at'] = formatDateTime($report->created_at);
            $arr['image'] = $report->image ? asset($report->image) : null;
            return $arr;
        });

        return response()->json([
            'success' => true,
            'data' => $reportsFormatted
        ]);
    }
}
