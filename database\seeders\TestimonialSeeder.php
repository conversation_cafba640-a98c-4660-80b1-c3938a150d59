<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Testimonial;

class TestimonialSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $testimonials = [
            [
                'name' => '<PERSON>',
                'title' => 'Community Leader',
                'content' => 'EcoSolve đã giúp khu phố của chúng tôi giải quyết vấn đề ô nhiễm nước một cách hiệu quả. Nền tảng này thực sự tạo ra sự khác biệt!',
                'display_order' => 1,
                'is_active' => true,
            ],
            [
                'name' => '<PERSON>',
                'title' => 'Environmental Expert',
                'content' => 'Là chuyên gia trên nền tảng, tôi đã có cơ hội hỗ trợ nhiều dự án môi trường ý nghĩa. EcoSolve kết nối chúng ta với những người thực sự quan tâm.',
                'display_order' => 2,
                'is_active' => true,
            ],
            [
                'name' => '<PERSON>',
                'title' => 'City Council Member',
                'content' => 'EcoSolve đã thay đổi cách thành phố chúng tôi tiếp cận các vấn đề môi trường. Dữ liệu từ nền tảng giúp chúng tôi đưa ra quyết định chính sách tốt hơn.',
                'display_order' => 3,
                'is_active' => true,
            ],
        ];

        foreach ($testimonials as $testimonial) {
            Testimonial::create($testimonial);
        }
    }
}
