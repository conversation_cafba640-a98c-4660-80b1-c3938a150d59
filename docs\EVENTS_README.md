# EcoSolves Events System

## Tổng quan
Hệ thống sự kiện môi trường với QR code và dữ liệu thực tế.

## Tính năng chính

### 1. **Hiển thị sự kiện**
- ✅ Ảnh mặc định khi không có ảnh hoặc ảnh lỗi
- ✅ Trạng thái thời gian: S<PERSON><PERSON> ra, <PERSON><PERSON>a, <PERSON><PERSON> kết thúc
- ✅ Thông tin đầy đủ: thờ<PERSON> gian, đ<PERSON><PERSON> điể<PERSON>, số người tham gia
- ✅ Responsive design

### 2. **QR Code cho sự kiện**
- ✅ Hiển thị QR code cho người chưa đăng nhập
- ✅ Nút đăng ký cho người đã đăng nhập
- ✅ Chức năng sao chép liên kết
- ✅ Error handling và fallback

### 3. **D<PERSON> liệu thực tế**
- ✅ 6 loại sự kiện môi trường cụ thể
- ✅ Địa điểm thực tế tại Việt Nam
- ✅ Nội dung chi tiết và ý nghĩa
- ✅ Hình ảnh phù hợp với từng loại sự kiện

## Cách sử dụng

### Chạy seeder để tạo dữ liệu:
```bash
# Tạo dữ liệu mới
php artisan db:seed --class=EventSeeder

# Refresh tất cả sự kiện
php artisan db:seed --class=RefreshEventsSeeder

# Chạy toàn bộ seeder
php artisan db:seed
```

### Cập nhật translation:
```bash
php artisan db:seed --class=EventTranslationSeeder
```

## Cấu trúc file

### Models & Factories
- `app/Models/Event.php` - Model sự kiện
- `database/factories/EventFactory.php` - Factory với dữ liệu thực tế
- `database/seeders/EventSeeder.php` - Seeder chính
- `database/seeders/RefreshEventsSeeder.php` - Seeder refresh

### Views
- `resources/views/pages/event.blade.php` - Danh sách sự kiện
- `resources/views/pages/event-show.blade.php` - Chi tiết sự kiện với QR code

### Assets
- `public/images/default-event.svg` - Ảnh mặc định cho sự kiện

### Translations
- `database/seeders/EventTranslationSeeder.php` - Bản dịch cho sự kiện

## Các loại sự kiện

1. **Chiến dịch dọn rác bãi biển** - Vũng Tàu
2. **Ngày hội trồng cây xanh** - Công viên Tao Đàn
3. **Hội thảo tái chế rác thải nhựa** - TP.HCM
4. **Làm sạch kênh rạch** - Quận 4
5. **Chợ phiên sản phẩm tái chế** - Công viên 23/9
6. **Đạp xe vì môi trường** - Bờ sông Sài Gòn

## Trạng thái sự kiện

- **Sắp diễn ra** (40%): Màu vàng, chưa bắt đầu
- **Đang diễn ra** (30%): Màu xanh, đang trong thời gian
- **Đã kết thúc** (30%): Màu xám, đã qua thời gian

## QR Code Features

- Tự động tạo QR code chứa URL sự kiện
- Fallback khi không thể tạo QR code
- Copy URL functionality
- Responsive design
- Error handling

## Technical Notes

- Sử dụng QRious library cho QR code generation
- Vanilla JavaScript (không phụ thuộc jQuery)
- SVG default image cho performance tốt
- Lazy loading cho images
- Error handling cho broken images
