<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Account;
use Illuminate\Support\Facades\Log;

class ApiLeaderboardController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/app/leaderboard/top",
     *     tags={"Leaderboard"},
     *     summary="Get top members with highest points",
     *     description="Retrieve top members ranked by points (default 10, max 50)",
     *     @OA\Parameter(
     *         name="limit",
     *         in="query",
     *         description="Number of top members to retrieve (1-50)",
     *         required=false,
     *         @OA\Schema(type="integer", default=10, minimum=1, maximum=50)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Top members retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(
     *                     property="leaderboard",
     *                     type="array",
     *                     @OA\Items(
     *                         @OA\Property(property="rank", type="integer", example=1),
     *                         @OA\Property(property="id", type="integer", example=123),
     *                         @OA\Property(property="username", type="string", example="john_doe"),
     *                         @OA\Property(property="full_name", type="string", example="John Doe"),
     *                         @OA\Property(property="avatar", type="string", nullable=true, example="http://example.com/avatar.jpg"),
     *                         @OA\Property(property="points", type="integer", example=1500),
     *                         @OA\Property(property="member_since", type="string", example="2024-01-15"),
     *                         @OA\Property(property="member_since_formatted", type="string", example="3 months ago")
     *                     )
     *                 ),
     *                 @OA\Property(property="total_active_members", type="integer", example=250),
     *                 @OA\Property(property="showing_top", type="integer", example=10),
     *                 @OA\Property(property="last_updated", type="string", example="2024-12-20 10:30:45")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Server error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="data", type="null"),
     *             @OA\Property(property="message", type="string", example="Failed to retrieve leaderboard")
     *         )
     *     )
     * )
     */
    public function topMembers(Request $request)
    {
        try {
            // Validate limit parameter (optional)
            $limit = $request->get('limit', 10);
            $limit = max(1, min($limit, 50)); // Min 1, Max 50

            // Get top members with highest points
            $topMembers = Account::select([
                    'id',
                    'username', 
                    'full_name',
                    'avatar',
                    'point',
                    'created_at'
                ])
                ->where('is_active', true)
                ->where('point', '>', 0)
                ->orderBy('point', 'desc')
                ->orderBy('created_at', 'asc') // Tie-breaker: earlier registration wins
                ->limit($limit)
                ->get()
                ->map(function ($account, $index) {
                    return [
                        'rank' => $index + 1,
                        'id' => $account->id,
                        'username' => $account->username,
                        'full_name' => $account->full_name ?? '',
                        'avatar' => $account->avatar ? asset($account->avatar) : null,
                        'points' => $account->point,
                        'member_since' => formatDateTime($account->created_at, 'Y-m-d'),
                        'member_since_formatted' => $account->created_at->diffForHumans(),
                    ];
                });

            // Get total number of active members with points
            $totalActiveMembers = Account::where('is_active', true)
                ->where('point', '>', 0)
                ->count();

            return response()->json([
                'success' => true,
                'data' => [
                    'leaderboard' => $topMembers,
                    'total_active_members' => $totalActiveMembers,
                    'showing_top' => $limit,
                    'last_updated' => formatDateTime(now())
                ],
                'message' => 'Top members retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Leaderboard API error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'data' => null,
                'message' => 'Failed to retrieve leaderboard'
            ], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/app/leaderboard/member/{accountId}",
     *     tags={"Leaderboard"},
     *     summary="Get member rank by ID",
     *     description="Get specific member's rank and details",
     *     @OA\Parameter(
     *         name="accountId",
     *         in="path",
     *         description="Account ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Member rank retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=123),
     *                 @OA\Property(property="username", type="string", example="john_doe"),
     *                 @OA\Property(property="full_name", type="string", example="John Doe"),
     *                 @OA\Property(property="avatar", type="string", nullable=true, example="http://example.com/avatar.jpg"),
     *                 @OA\Property(property="points", type="integer", example=1500),
     *                 @OA\Property(property="rank", type="integer", example=5),
     *                 @OA\Property(property="member_since", type="string", example="2024-01-15"),
     *                 @OA\Property(property="member_since_formatted", type="string", example="3 months ago")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Member not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="data", type="null"),
     *             @OA\Property(property="message", type="string", example="Member not found or inactive")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Server error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="data", type="null"),
     *             @OA\Property(property="message", type="string", example="Failed to retrieve member rank")
     *         )
     *     )
     * )
     */
    public function getMemberRank(Request $request, $accountId)
    {
        try {
            $account = Account::where('id', $accountId)
                ->where('is_active', true)
                ->first();

            if (!$account) {
                return response()->json([
                    'success' => false,
                    'data' => null,
                    'message' => 'Member not found or inactive'
                ], 404);
            }

            // Calculate rank
            $rank = Account::where('is_active', true)
                ->where(function($query) use ($account) {
                    $query->where('point', '>', $account->point)
                        ->orWhere(function($q) use ($account) {
                            $q->where('point', $account->point)
                              ->where('created_at', '<', $account->created_at);
                        });
                })
                ->count() + 1;

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $account->id,
                    'username' => $account->username,
                    'full_name' => $account->full_name ?? '',
                    'avatar' => $account->avatar ? url($account->avatar) : null,
                    'points' => $account->point,
                    'rank' => $rank,
                    'member_since' => formatDateTime($account->created_at, 'Y-m-d'),
                    'member_since_formatted' => $account->created_at->diffForHumans(),
                ],
                'message' => 'Member rank retrieved successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Member rank API error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'data' => null,
                'message' => 'Failed to retrieve member rank'
            ], 500);
        }
    }
} 