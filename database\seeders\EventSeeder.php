<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use App\Models\Event;
use App\Models\Account;

class EventSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Đ<PERSON>m bảo có ít nhất 1 account để làm creator
        if (Account::count() === 0) {
            Account::factory()->create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
            ]);
        }

        // Tạo các sự kiện với status 'approved' nhưng thời gian khác nhau

        // 1. Sự kiện sắp diễn ra (chưa b<PERSON>t đầu) - 40%
        Event::factory(20)->upcoming()->create();

        // 2. Sự kiện đang diễn ra - 30%
        Event::factory(15)->ongoing()->create();

        // 3. Sự kiện đã kết thúc - 30%
        Event::factory(15)->finished()->create();
        
        // Tạo thêm một số sự kiện đặc biệt với tiêu đề cụ thể - tất cả đều approved
        $specialEvents = [
            [
                'title' => 'Ngày Hội Dọn Rác Thế Giới 2025',
                'description' => 'Tham gia cùng hàng nghìn tình nguyện viên trong ngày hội dọn rác lớn nhất năm, góp phần tạo nên một thế giới xanh sạch đẹp.',
                'content' => 'Ngày Hội Dọn Rác Thế Giới là sự kiện môi trường lớn nhất trong năm, thu hút sự tham gia của cộng đồng toàn cầu. Tại Việt Nam, chúng tôi tổ chức hoạt động tại nhiều địa điểm khác nhau, tập trung vào các khu vực công cộng, bãi biển, công viên và khu dân cư. Mỗi người tham gia sẽ được trang bị đầy đủ dụng cụ dọn dẹp, găng tay và túi rác. Hoạt động không chỉ dừng lại ở việc thu gom mà còn phân loại rác để tái chế, giáo dục cộng đồng về tác hại của rác thải và cách giảm thiểu chất thải trong đời sống hàng ngày.',
                'location' => 'Công viên Tao Đàn, Quận 1, TP.HCM',
                'featured_image' => 'events/world-cleanup-day-2025.jpg',
                'time_type' => 'upcoming',
            ],
            [
                'title' => 'Chiến dịch "Sóng xanh" - Bảo vệ biển Đông',
                'description' => 'Hoạt động làm sạch bãi biển, tuyên truyền bảo vệ hệ sinh thái biển và giảm thiểu rác thải nhựa đại dương.',
                'content' => 'Biển Đông đang đối mặt với tình trạng ô nhiễm nghiêm trọng do rác thải nhựa và hoạt động của con người. Chiến dịch "Sóng xanh" là nỗ lực chung của cộng đồng nhằm bảo vệ và phục hồi hệ sinh thái biển. Chúng tôi sẽ tổ chức các hoạt động làm sạch bãi biển, thu gom rác thải nổi trên mặt nước, giáo dục ngư dân về tác hại của việc xả rác xuống biển. Sự kiện cũng bao gồm triển lãm ảnh về đời sống biển, workshop làm đồ thủ công từ vỏ sò ốc và các hoạt động giải trí lành mạnh cho trẻ em.',
                'location' => 'Bãi biển Thùy Vân, Vũng Tàu',
                'featured_image' => 'events/blue-wave-campaign.jpg',
                'time_type' => 'ongoing',
            ],
            [
                'title' => 'Hội chợ "Xanh - Sạch - Đẹp"',
                'description' => 'Triển lãm và bán các sản phẩm thân thiện môi trường, thúc đẩy lối sống xanh và tiêu dùng bền vững.',
                'content' => 'Hội chợ "Xanh - Sạch - Đẹp" là nơi quy tụ các sản phẩm và dịch vụ thân thiện với môi trường. Từ thực phẩm hữu cơ, đồ dùng tái chế đến các giải pháp năng lượng sạch, hội chợ mang đến cho người tiêu dùng nhiều lựa chọn xanh. Các gian hàng được thiết kế từ vật liệu tái chế, sử dụng năng lượng mặt trời và không sử dụng túi nilon. Sự kiện cũng có các workshop hướng dẫn làm sản phẩm handmade, nấu ăn với nguyên liệu hữu cơ và tư vấn về lối sống bền vững.',
                'location' => 'Công viên Văn hóa Đầm Sen, Quận 11, TP.HCM',
                'featured_image' => 'events/green-clean-beautiful-fair.jpg',
                'time_type' => 'finished',
            ],
            [
                'title' => 'Trồng rừng "Phổi xanh thành phố"',
                'description' => 'Hoạt động trồng cây quy mô lớn nhằm tạo ra những "phổi xanh" cho đô thị, cải thiện chất lượng không khí.',
                'content' => 'Dự án "Phổi xanh thành phố" là sáng kiến dài hạn nhằm tăng diện tích cây xanh trong đô thị. Chúng tôi sẽ trồng các loại cây bản địa có khả năng hấp thụ CO2 cao và thích nghi tốt với khí hậu nhiệt đới. Mỗi cây được trồng sẽ có mã QR để theo dõi quá trình phát triển. Người tham gia sẽ được đào tạo về kỹ thuật trồng cây, chăm sóc và bảo vệ cây xanh. Dự án cũng kết hợp với các trường học để giáo dục trẻ em về tầm quan trọng của rừng cây đối với môi trường.',
                'location' => 'Khu đô thị Ecopark, Văn Giang, Hưng Yên',
                'featured_image' => 'events/urban-forest-project.jpg',
                'time_type' => 'upcoming',
            ],
            [
                'title' => 'Ngày hội "Xe đạp xanh"',
                'description' => 'Khuyến khích sử dụng xe đạp như phương tiện giao thông xanh, giảm thiểu khí thải và ô nhiễm không khí.',
                'content' => 'Giao thông xanh là giải pháp bền vững cho vấn đề ô nhiễm không khí đô thị. Ngày hội "Xe đạp xanh" nhằm tuyên truyền lợi ích của việc sử dụng xe đạp trong di chuyển hàng ngày. Sự kiện bao gồm parade xe đạp qua các tuyến phố chính, triển lãm các loại xe đạp thân thiện môi trường, workshop bảo dưỡng xe đạp và các trò chơi giải trí. Chúng tôi cũng tổ chức thi đua "Thử thách 30 ngày đi xe đạp" để khuyến khích người dân thay đổi thói quen di chuyển.',
                'location' => 'Phố đi bộ Nguyễn Huệ, Quận 1, TP.HCM',
                'featured_image' => 'events/green-bicycle-day.jpg',
                'time_type' => 'upcoming',
            ],
        ];

        foreach ($specialEvents as $eventData) {
            $timeType = $eventData['time_type'];
            unset($eventData['time_type']);

            // Tạo thời gian dựa trên loại
            switch ($timeType) {
                case 'upcoming':
                    $start = now()->addDays(rand(1, 30));
                    $end = (clone $start)->addHours(rand(3, 8));
                    break;
                case 'ongoing':
                    $start = now()->subDays(rand(1, 3));
                    $end = now()->addDays(rand(1, 3));
                    break;
                case 'finished':
                    $start = now()->subDays(rand(7, 60));
                    $end = (clone $start)->addHours(rand(3, 8));
                    break;
            }

            // Tạo slug từ title
            $slug = Str::slug($eventData['title']) . '-' . Str::random(8);

            Event::create(array_merge($eventData, [
                'slug' => $slug,
                'start_date' => $start,
                'end_date' => $end,
                'status' => 'approved',
                'is_active' => true,
                'max_participants' => rand(100, 500),
                'created_by' => Account::inRandomOrder()->value('id') ?? 1,
            ]));
        }

        $this->command->info('Đã tạo ' . Event::count() . ' sự kiện - tất cả đều approved!');

        // Thống kê theo thời gian
        $now = now();
        $upcoming = Event::where('start_date', '>', $now)->count();
        $ongoing = Event::where('start_date', '<=', $now)
                       ->where(function($q) use ($now) {
                           $q->whereNull('end_date')->orWhere('end_date', '>=', $now);
                       })->count();
        $finished = Event::whereNotNull('end_date')->where('end_date', '<', $now)->count();

        $this->command->info('- Sắp diễn ra: ' . $upcoming);
        $this->command->info('- Đang diễn ra: ' . $ongoing);
        $this->command->info('- Đã kết thúc: ' . $finished);
    }
}
