@extends('layouts.app')

@section('title') }}')

@section('header') }}')

@section('content')
    <div class="mb-4 flex items-center text-sm text-gray-500">
        <span>{{ $post->created_at->format('d/m/Y') }}</span>
        @if($post->category)
            <span class="mx-1">•</span>
            <a href="{{ route('categories.show', $post->category) }}" class="text-indigo-500 hover:text-indigo-700">{{ $post->category->name }}</a>
        @endif
    </div>

    @if($post->featured_image)
        <div class="mb-6">
            <img src="{{ $post->featured_image }}" alt="{{ $post->title }}" class="w-full h-auto rounded-lg shadow-md-8">
        {!! $post->content !!}
    </div>

    @if(isset($post->tags) && count($post->tags) > 0)
        <div class="mb-6">
            <h3 class="text-lg font-semibold mb-2">Tags:</h3>
            <div class="flex flex-wrap gap-2">
                @foreach($post->tags as $tag)
                    <a href="{{ route('tags.show', $tag) }}" class="px-3 py-1 bg-gray-100 text-gray-800 text-sm rounded-full hover:bg-gray-200">
                        #{{ $tag->name }}
                    </a>
                @endforeach
            </div>
        </div>
    @endif

    @if(isset($relatedPosts) && count($relatedPosts) > 0)
        <div class="border-t pt-6 mt-6">
            <h3 class="text-lg font-semibold mb-4">{{ trans_db('general.bai_viet_lien_quanh3_div_class') }}
                @foreach($relatedPosts as $relatedPost)
                    <div class="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                        @if($relatedPost->featured_image)
                            <img src="{{ $relatedPost->featured_image }}" alt="{{ $relatedPost->title }}" class="w-full h-40 object-cover-4">
                            <h4 class="font-semibold mb-2">
                                <a href="{{ route('posts.show', $relatedPost) }}" class="text-indigo-600 hover:text-indigo-800">{{ $relatedPost->title }}</a>
                            </h4>
                            <div class="text-xs text-gray-500 mb-2">
                                <span>{{ $relatedPost->created_at->format('d/m/Y') }}</span>
                            </div>
                            <a href="{{ route('posts.show', $relatedPost) }}" class="text-sm text-indigo-600 hover:text-indigo-800 font-medium') }}
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @endif

    <div class="mt-6 flex justify-between">
        @if(isset($previousPost))
            <a href="{{ route('posts.show', $previousPost) }}" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-300 active:bg-gray-400 focus:outline-none focus:border-gray-400 focus:ring ring-gray-200 disabled:opacity-25 transition ease-in-out duration-150">
                ← Bài trước
            </a>
        @else
            <div></div>
        @endif

        @if(isset($nextPost))
            <a href="{{ route('posts.show', $nextPost) }}" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-300 active:bg-gray-400 focus:outline-none focus:border-gray-400 focus:ring ring-gray-200 disabled:opacity-25 transition ease-in-out duration-150">
                Bài tiếp theo →
            </a>
        @else
            <div></div>
        @endif
    </div>

    <div class="mt-4 text-center('posts.index') }}" class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 active:bg-indigo-800 focus:outline-none focus:border-indigo-800 focus:ring ring-indigo-300 disabled:opacity-25 transition ease-in-out duration-150">
            Quay lại danh sách bài viết
        </a>
    </div>
@endsection