<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Notification;

class ApiNotificationController extends Controller
{
    /**
     * @OA\Post(
     *     path="/api/app/notifications",
     *     tags={"Notifications"},
     *     summary="<PERSON><PERSON>y danh sách thông báo của user",
     *     description="<PERSON><PERSON>y danh sách thông báo của user hiện tại, c<PERSON> phân trang chuẩn <PERSON>. Yêu cầu đăng nhập (sanctum)",
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Số trang",
     *         required=false,
     *         @OA\Schema(type="integer", default=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Số thông báo mỗi trang (tối đa 50)",
     *         required=false,
     *         @OA\Schema(type="integer", default=15, maximum=50)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="array",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="title", type="string", example="Báo cáo đã được duyệt!"),
     *                     @OA\Property(property="body", type="string", example="Báo cáo: ... đã được phê duyệt và cộng điểm."),
     *                     @OA\Property(property="is_read", type="integer", example=0),
     *                     @OA\Property(property="created_at", type="string", example="2024-07-01 10:00:00")
     *                 )
     *             ),
     *             @OA\Property(property="pagination", type="object",
     *                 @OA\Property(property="current_page", type="integer", example=1),
     *                 @OA\Property(property="per_page", type="integer", example=15),
     *                 @OA\Property(property="total", type="integer", example=20),
     *                 @OA\Property(property="last_page", type="integer", example=2),
     *                 @OA\Property(property="has_more", type="boolean", example=false)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Chưa đăng nhập",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Bạn cần đăng nhập!")
     *         )
     *     )
     * )
     */
    public function index(Request $request)
    {
        $user = $request->user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => 'Bạn cần đăng nhập!'], 401);
        }
        $perPage = min($request->get('per_page', 15), 50);
        $notifications = Notification::where('account_id', $user->id)
            ->orderByDesc('created_at')
            ->paginate($perPage);
        $data = collect($notifications->items())->map(function($item) {
            return [
                'id' => $item->id,
                'title' => $item->title,
                'body' => $item->body,
                'is_read' => $item->is_read,
                'created_at' => $item->created_at ? formatDateTime($item->created_at) : null,
            ];
        });
        return response()->json([
            'success' => true,
            'data' => $data,
            'pagination' => [
                'current_page' => $notifications->currentPage(),
                'per_page' => $notifications->perPage(),
                'total' => $notifications->total(),
                'last_page' => $notifications->lastPage(),
                'has_more' => $notifications->hasMorePages(),
            ]
        ]);
    }

    /**
     * @OA\Post(
     *     path="/api/app/notifications/{id}/read",
     *     tags={"Notifications"},
     *     summary="Đánh dấu thông báo đã đọc",
     *     description="Đánh dấu một thông báo là đã đọc cho user hiện tại. Yêu cầu đăng nhập (sanctum)",
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="ID của thông báo",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Đã đánh dấu là đã đọc!"),
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="is_read", type="boolean", example=true)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Chưa đăng nhập",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Bạn cần đăng nhập!")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Không tìm thấy thông báo",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Không tìm thấy thông báo!")
     *         )
     *     )
     * )
     */
    public function markAsRead(Request $request, $id)
    {
        $user = $request->user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => 'Bạn cần đăng nhập!'], 401);
        }
        $notification = \App\Models\Notification::where('id', $id)->where('account_id', $user->id)->first();
        if (!$notification) {
            return response()->json(['success' => false, 'message' => 'Không tìm thấy thông báo!'], 404);
        }
        $notification->is_read = 1;
        $notification->save();
        return response()->json(['success' => true, 'message' => 'Đã đánh dấu là đã đọc!', 'data' => $notification]);
    }

    /**
     * @OA\Post(
     *     path="/api/app/notifications/has-unread",
     *     tags={"Notifications"},
     *     summary="Kiểm tra user có thông báo chưa đọc không",
     *     description="Trả về true nếu user hiện tại có ít nhất 1 thông báo chưa đọc, ngược lại trả về false. Yêu cầu đăng nhập (sanctum)",
     *     security={{"sanctum": {}}},
     *     @OA\Response(
     *         response=200,
     *         description="Thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="has_unread", type="boolean", example=true)
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Chưa đăng nhập",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Bạn cần đăng nhập!")
     *         )
     *     )
     * )
     */
    public function hasUnread(Request $request)
    {
        $user = $request->user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => 'Bạn cần đăng nhập!'], 401);
        }
        $hasUnread = \App\Models\Notification::where('account_id', $user->id)->where('is_read', 0)->exists();
        return response()->json([
            'success' => true,
            'has_unread' => $hasUnread
        ]);
    }
} 