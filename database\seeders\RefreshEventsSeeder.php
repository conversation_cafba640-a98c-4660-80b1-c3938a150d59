<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use App\Models\Event;
use App\Models\Account;

class RefreshEventsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * Xóa tất cả sự kiện cũ và tạo mới với đủ các trạng thái thời gian
     */
    public function run(): void
    {
        // Xóa tất cả sự kiện cũ
        Event::truncate();
        $this->command->info('Đã xóa tất cả sự kiện cũ');

        // Đảm bảo có ít nhất 1 account để làm creator
        if (Account::count() === 0) {
            Account::factory()->create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
            ]);
        }

        // Tạo các sự kiện với status 'approved' nhưng thời gian khác nhau
        
        // 1. Sự kiện sắp diễn ra (chưa bắt đầu) - 40%
        Event::factory(20)->upcoming()->create();
        
        // 2. Sự kiện đang diễn ra - 30%
        Event::factory(15)->ongoing()->create();
        
        // 3. Sự kiện đã kết thúc - 30%
        Event::factory(15)->finished()->create();
        
        // Tạo thêm một số sự kiện đặc biệt với tiêu đề cụ thể - tất cả đều approved
        $specialEvents = [
            [
                'title' => 'Ngày Hội Dọn Rác Thế Giới 2025',
                'description' => 'Tham gia cùng chúng tôi trong ngày hội dọn rác lớn nhất năm 2025, góp phần bảo vệ môi trường và tạo nên một thế giới xanh sạch đẹp.',
                'location' => 'Công viên Tao Đàn, Quận 1, TP.HCM',
                'time_type' => 'upcoming', // Sắp diễn ra
            ],
            [
                'title' => 'Chiến Dịch Làm Sạch Bãi Biển Vũng Tàu',
                'description' => 'Cùng nhau bảo vệ bãi biển xinh đẹp của Vũng Tàu, thu gom rác thải và nâng cao ý thức bảo vệ môi trường biển.',
                'location' => 'Bãi biển Vũng Tàu, Bà Rịa - Vũng Tàu',
                'time_type' => 'ongoing', // Đang diễn ra
            ],
            [
                'title' => 'Hội Thảo Tái Chế Rác Thải Nhựa',
                'description' => 'Học cách tái chế rác thải nhựa thành những sản phẩm hữu ích, góp phần giảm thiểu ô nhiễm môi trường.',
                'location' => 'Trung tâm Văn hóa Quận 3, TP.HCM',
                'time_type' => 'finished', // Đã kết thúc
            ],
            [
                'title' => 'Trồng Cây Xanh Tại Công Viên Thống Nhất',
                'description' => 'Hoạt động trồng cây xanh nhằm cải thiện không khí và tạo không gian xanh cho cộng đồng.',
                'location' => 'Công viên Thống Nhất, Hai Bà Trưng, Hà Nội',
                'time_type' => 'upcoming', // Sắp diễn ra
            ],
        ];

        foreach ($specialEvents as $eventData) {
            $timeType = $eventData['time_type'];
            unset($eventData['time_type']);
            
            // Tạo thời gian dựa trên loại
            switch ($timeType) {
                case 'upcoming':
                    $start = now()->addDays(rand(1, 30));
                    $end = (clone $start)->addHours(rand(2, 6));
                    break;
                case 'ongoing':
                    $start = now()->subDays(rand(1, 3));
                    $end = now()->addDays(rand(1, 3));
                    break;
                case 'finished':
                    $start = now()->subDays(rand(7, 60));
                    $end = (clone $start)->addHours(rand(2, 6));
                    break;
            }
            
            Event::factory()->create(array_merge($eventData, [
                'start_date' => $start,
                'end_date' => $end,
                'status' => 'approved',
                'is_active' => true,
                'max_participants' => rand(50, 200),
                'created_by' => Account::inRandomOrder()->value('id'),
            ]));
        }

        $this->command->info('Đã tạo ' . Event::count() . ' sự kiện mới - tất cả đều approved!');
        
        // Thống kê theo thời gian
        $now = now();
        $upcoming = Event::where('start_date', '>', $now)->count();
        $ongoing = Event::where('start_date', '<=', $now)
                       ->where(function($q) use ($now) {
                           $q->whereNull('end_date')->orWhere('end_date', '>=', $now);
                       })->count();
        $finished = Event::whereNotNull('end_date')->where('end_date', '<', $now)->count();
        
        $this->command->info("- Sắp diễn ra: {$upcoming}");
        $this->command->info("- Đang diễn ra: {$ongoing}");
        $this->command->info("- Đã kết thúc: {$finished}");
    }
}
