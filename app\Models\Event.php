<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Event extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'content',
        'location',
        'start_date',
        'end_date',
        'featured_image',
        'is_active',
        'max_participants',
        'status',
        'created_by',
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'is_active' => 'boolean',
        'status' => 'string',
        'created_at' => 'datetime',
    ];

    /**
     * Get all of the event's messages.
     */
    public function messages()
    {
        return $this->morphMany(Message::class, 'messageable');
    }

    public function creator()
    {
        return $this->belongsTo(\App\Models\Account::class, 'created_by');
    }

    public function registrations()
    {
        return $this->hasMany(\App\Models\EventRegistration::class, 'event_id');
    }
}
