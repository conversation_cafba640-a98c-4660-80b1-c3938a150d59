@extends('layouts.app')

@section('title', trans_db('general.ban_do_diem_rac') ?? 'Bản đồ điểm rác')

@section('header', trans_db('general.ban_do_diem_rac_tap_trung') ?? '<PERSON><PERSON>n đồ điểm rác tập trung')

@section('content')
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />

    <div class="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
        <!-- Header Section -->
        <div class="bg-white shadow-sm border-b">
            <div class="container mx-auto px-4 py-6">
                <div class="text-center">
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">
                        {{ trans_db('general.ban_do_diem_rac_tap_trung') ?? '<PERSON><PERSON><PERSON> đồ điểm rác tập trung' }}
                    </h1>
                    <p class="text-gray-600">
                        {{ trans_db('general.xem_va_theo_doi_cac_diem_rac_da_bao_cao') ?? 'Xem và theo dõi các điểm rác đã báo cáo trong khu vực' }}
                    </p>
                    <div class="flex justify-center items-center gap-4 mt-4">
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                            <span class="text-sm text-gray-600">{{ trans_db('general.diem_rac_can_xu_ly') ?? 'Điểm rác cần xử lý' }}</span>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span class="text-sm text-gray-600">{{ trans_db('general.da_duoc_duyet') ?? 'Đã được duyệt' }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="container mx-auto px-4 py-8">
            <div class="flex flex-col md:flex-row gap-6">
                <!-- Sidebar - Danh sách điểm rác -->
                <div class="w-full md:w-1/3 max-h-[70vh] overflow-y-auto">
                    <div class="bg-white rounded-xl shadow-lg h-full flex flex-col">
                        <!-- Header -->
                        <div class="p-6 border-b border-gray-100">
                            <div class="flex items-center justify-between">
                                <h3 class="text-xl font-semibold text-gray-800">
                                    {{ trans_db('general.danh_sach_diem_rac') ?? 'Danh sách điểm rác' }}
                                </h3>
                                <span class="bg-green-100 text-green-800 text-sm font-medium px-3 py-1 rounded-full">
                                    {{ count($reports) }} {{ trans_db('general.diem') ?? 'điểm' }}
                                </span>
                            </div>
                        </div>

                        <!-- List -->
                        <div class="flex-1 overflow-y-auto p-4">
                            <div id="report-list" class="space-y-3">
                                @forelse ($reports as $i => $r)
                                    <div class="report-item group cursor-pointer bg-gray-50 hover:bg-green-50 border border-gray-200 hover:border-green-300 rounded-lg p-4 transition-all duration-200 hover:shadow-md"
                                         data-index="{{ $i }}"
                                         data-lat="{{ $r['latitude'] }}"
                                         data-lng="{{ $r['longitude'] }}">
                                        <!-- Header -->
                                        <div class="flex items-start justify-between mb-3">
                                            <div class="flex items-center gap-3">
                                                <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                                                    {{ $i + 1 }}
                                                </div>
                                                <div class="flex-1">
                                                    <h4 class="font-medium text-gray-800 group-hover:text-green-700 transition-colors">
                                                        {{ trans_db('general.diem_rac') ?? 'Điểm rác' }} #{{ $i + 1 }}
                                                    </h4>
                                                    <div class="flex items-center gap-2 mt-1">
                                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                            {{ ucfirst($r['status']) }}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <button class="opacity-0 group-hover:opacity-100 transition-opacity text-green-600 hover:text-green-700">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                </svg>
                                            </button>
                                        </div>

                                        <!-- Description -->
                                        <p class="text-sm text-gray-600 mb-3 line-clamp-2">
                                            {{ Str::limit($r['description'], 80) }}
                                        </p>

                                        <!-- Image -->
                                        @if ($r['image'])
                                            <div class="mb-3">
                                                <img src="/storage/{{ $r['image'] }}"
                                                     alt="{{ trans_db('general.anh_diem_rac') ?? 'Ảnh điểm rác' }}"
                                                     class="w-full h-24 object-cover rounded-lg">
                                            </div>
                                        @endif

                                        <!-- Coordinates -->
                                        <div class="flex items-center gap-4 text-xs text-gray-500 mb-3">
                                            <div class="flex items-center gap-1">
                                                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                                                </svg>
                                                <span>{{ number_format($r['latitude'], 4) }}, {{ number_format($r['longitude'], 4) }}</span>
                                            </div>
                                        </div>

                                        <!-- Actions -->
                                        <div class="flex items-center justify-between">
                                            <button class="view-on-map text-green-600 hover:text-green-700 text-sm font-medium flex items-center gap-1">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                </svg>
                                                {{ trans_db('action.xem_tren_ban_do') ?? 'Xem trên bản đồ' }}
                                            </button>
                                            @if ($r['gmap_link'])
                                                <a href="{{ $r['gmap_link'] }}" target="_blank"
                                                   class="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center gap-1">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                                    </svg>
                                                    Google Maps
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                @empty
                                    <div class="text-center py-12">
                                        <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                        <h3 class="text-lg font-medium text-gray-900 mb-2">{{ trans_db('general.chua_co_diem_rac_nao') ?? 'Chưa có điểm rác nào' }}</h3>
                                        <p class="text-gray-500">{{ trans_db('general.hay_bao_cao_diem_rac_dau_tien') ?? 'Hãy báo cáo điểm rác đầu tiên của bạn' }}</p>
                                    </div>
                                @endforelse
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Map Section -->
                <div class="w-full md:w-2/3 max-h-[70vh]">
                    <div class="bg-white rounded-xl shadow-lg h-full flex flex-col">
                        <!-- Map Header -->
                        <div class="p-6 border-b border-gray-100">
                            <div class="flex items-center justify-between">
                                <h3 class="text-xl font-semibold text-gray-800">
                                    {{ trans_db('general.ban_do_tuong_tac') ?? 'Bản đồ tương tác' }}
                                </h3>
                                <div class="flex items-center gap-2">
                                    <button id="reset-view" class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                        {{ trans_db('action.reset_view') ?? 'Reset View' }}
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Map Container -->
                        <div class="flex-1 relative">
                            <div id="map" class="w-full h-full rounded-b-xl"></div>

                            <!-- Selected Point Info -->
                            <div id="selected-point-info" class="absolute top-4 left-4 bg-white rounded-lg shadow-lg p-4 max-w-sm hidden">
                                <div class="flex items-start justify-between mb-2">
                                    <h4 class="font-semibold text-gray-800" id="point-title"></h4>
                                    <button id="close-info" class="text-gray-400 hover:text-gray-600">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                </div>
                                <p class="text-sm text-gray-600 mb-3" id="point-description"></p>
                                <div class="text-xs text-gray-500" id="point-coordinates"></div>
                            </div>

                            <!-- No map message -->
                            <div id="no-map-msg" class="absolute inset-0 flex items-center justify-center text-center text-gray-500 hidden">
                                <div>
                                    <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m0 0L9 7"></path>
                                    </svg>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">{{ trans_db('general.khong_co_ban_do') ?? 'Không có bản đồ để hiển thị' }}</h3>
                                    <p class="text-gray-500">{{ trans_db('general.chua_co_diem_rac_nao_de_hien_thi') ?? 'Chưa có điểm rác nào để hiển thị trên bản đồ' }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // Dữ liệu reports từ PHP
        var reports = @json($reports ?? []);
        var map, markers = [];
        var selectedMarker = null;

        // Khởi tạo bản đồ
        function initMap() {
            // Khởi tạo bản đồ Leaflet với view mặc định tại TP.HCM
            map = L.map('map', {
                zoomControl: true,
                scrollWheelZoom: true,
                doubleClickZoom: true,
                touchZoom: true
            }).setView([10.8231, 106.6297], 11);

            // Thêm tile layer với style đẹp hơn
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© {{ trans_db("general.openstreetmap") ?? "OpenStreetMap" }} contributors',
                maxZoom: 19
            }).addTo(map);

            // Tạo custom icon cho marker
            var trashIcon = L.divIcon({
                className: 'custom-marker',
                html: `<div class="w-8 h-8 bg-red-500 border-2 border-white rounded-full shadow-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clip-rule="evenodd"></path>
                            <path fill-rule="evenodd" d="M4 5a1 1 0 011-1h10a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM6 8a1 1 0 011 1v6a1 1 0 001 1h4a1 1 0 001-1V9a1 1 0 112 0v6a3 3 0 01-3 3H8a3 3 0 01-3-3V9a1 1 0 011-1z" clip-rule="evenodd"></path>
                        </svg>
                       </div>`,
                iconSize: [32, 32],
                iconAnchor: [16, 16],
                popupAnchor: [0, -16]
            });

            var selectedIcon = L.divIcon({
                className: 'custom-marker',
                html: `<div class="w-10 h-10 bg-green-500 border-3 border-white rounded-full shadow-xl flex items-center justify-center animate-pulse">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clip-rule="evenodd"></path>
                            <path fill-rule="evenodd" d="M4 5a1 1 0 011-1h10a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM6 8a1 1 0 011 1v6a1 1 0 001 1h4a1 1 0 001 1V9a1 1 0 112 0v6a3 3 0 01-3 3H8a3 3 0 01-3-3V9a1 1 0 011-1z" clip-rule="evenodd"></path>
                        </svg>
                       </div>`,
                iconSize: [40, 40],
                iconAnchor: [20, 20],
                popupAnchor: [0, -20]
            });

            // Tạo marker cho từng báo cáo
            reports.forEach(function(r, idx) {
                if (!r.latitude || !r.longitude) return;

                var popupHtml = `
                    <div class="min-w-[250px] max-w-[300px]">
                        <div class="mb-3">
                            <h4 class="font-semibold text-gray-800 mb-1">{{ trans_db('general.diem_rac') ?? 'Điểm rác' }} #${idx + 1}</h4>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                ${r.status.charAt(0).toUpperCase() + r.status.slice(1)}
                            </span>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">${r.description || '{{ trans_db('general.khong_co_mo_ta') ?? 'Không có mô tả' }}'}</p>
                        ${r.image ? `<img src='/storage/${r.image}' alt="{{ trans_db('general.anh_diem_rac') ?? 'Ảnh điểm rác' }}" class="w-full h-32 object-cover rounded-lg mb-3">` : ''}
                        <div class="text-xs text-gray-500 mb-3">
                            <div class="flex items-center gap-1">
                                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                                </svg>
                                ${parseFloat(r.latitude).toFixed(4)}, ${parseFloat(r.longitude).toFixed(4)}
                            </div>
                        </div>
                        ${r.gmap_link ? `<a href='${r.gmap_link}' target='_blank' class='inline-flex items-center gap-1 text-blue-600 hover:text-blue-700 text-sm font-medium'>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                            </svg>
                            {{ trans_db('action.xem_tren_google_maps') ?? 'Xem trên Google Maps' }}
                        </a>` : ''}
                    </div>
                `;

                var marker = L.marker([r.latitude, r.longitude], {
                    icon: trashIcon,
                    riseOnHover: true
                })
                .addTo(map)
                .bindPopup(popupHtml, {
                    maxWidth: 300,
                    className: 'custom-popup'
                });

                // Lưu reference để sử dụng sau
                marker.reportIndex = idx;
                marker.normalIcon = trashIcon;
                marker.selectedIcon = selectedIcon;

                markers.push(marker);
            });

            // Fit map để hiển thị tất cả markers
            if (markers.length > 0) {
                var group = new L.featureGroup(markers);
                map.fitBounds(group.getBounds().pad(0.1));
            }
        }

        // Hàm để highlight marker được chọn
        function selectMarker(index) {
            // Reset tất cả markers về trạng thái bình thường
            markers.forEach(function(marker) {
                marker.setIcon(marker.normalIcon);
            });

            // Highlight marker được chọn
            if (markers[index]) {
                selectedMarker = markers[index];
                selectedMarker.setIcon(selectedMarker.selectedIcon);

                // Di chuyển map đến vị trí marker và zoom in
                map.setView([reports[index].latitude, reports[index].longitude], 16, {
                    animate: true,
                    duration: 1
                });

                // Mở popup sau một chút delay
                setTimeout(function() {
                    selectedMarker.openPopup();
                }, 500);

                // Hiển thị thông tin điểm được chọn
                showSelectedPointInfo(index);
            }
        }

        // Hiển thị thông tin điểm được chọn
        function showSelectedPointInfo(index) {
            const info = document.getElementById('selected-point-info');
            const report = reports[index];

            document.getElementById('point-title').textContent = `{{ trans_db('general.diem_rac') ?? 'Điểm rác' }} #${index + 1}`;
            document.getElementById('point-description').textContent = report.description || '{{ trans_db('general.khong_co_mo_ta') ?? 'Không có mô tả' }}';
            document.getElementById('point-coordinates').textContent = `${parseFloat(report.latitude).toFixed(4)}, ${parseFloat(report.longitude).toFixed(4)}`;

            info.classList.remove('hidden');
        }

        // Ẩn thông tin điểm được chọn
        function hideSelectedPointInfo() {
            document.getElementById('selected-point-info').classList.add('hidden');
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Khởi tạo map
            if (reports.length > 0) {
                initMap();
            } else {
                document.getElementById('no-map-msg').classList.remove('hidden');
            }

            // Xử lý click vào item trong danh sách
            document.querySelectorAll('.report-item').forEach(function(item) {
                item.addEventListener('click', function() {
                    const index = parseInt(this.getAttribute('data-index'));

                    // Remove active class from all items
                    document.querySelectorAll('.report-item').forEach(function(el) {
                        el.classList.remove('bg-green-100', 'border-green-400');
                        el.classList.add('bg-gray-50', 'border-gray-200');
                    });

                    // Add active class to clicked item
                    this.classList.remove('bg-gray-50', 'border-gray-200');
                    this.classList.add('bg-green-100', 'border-green-400');

                    // Select marker on map
                    selectMarker(index);
                });
            });

            // Reset view button
            document.getElementById('reset-view')?.addEventListener('click', function() {
                if (markers.length > 0) {
                    // Reset all markers
                    markers.forEach(function(marker) {
                        marker.setIcon(marker.normalIcon);
                    });

                    // Fit bounds to show all markers
                    var group = new L.featureGroup(markers);
                    map.fitBounds(group.getBounds().pad(0.1));

                    // Remove active class from all items
                    document.querySelectorAll('.report-item').forEach(function(el) {
                        el.classList.remove('bg-green-100', 'border-green-400');
                        el.classList.add('bg-gray-50', 'border-gray-200');
                    });

                    hideSelectedPointInfo();
                }
            });

            // Close info panel
            document.getElementById('close-info')?.addEventListener('click', function() {
                hideSelectedPointInfo();
            });
        });
    </script>

    <style>
        .custom-popup .leaflet-popup-content-wrapper {
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .custom-popup .leaflet-popup-tip {
            background: white;
        }

        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .animate-pulse {
            animation: pulse 2s infinite;
        }
    </style>
@endsection
