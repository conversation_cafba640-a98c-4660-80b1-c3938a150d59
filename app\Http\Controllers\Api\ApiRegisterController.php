<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Session;
use App\Models\Account;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class ApiRegisterController extends Controller
{
    /**
     * @OA\Post(
     *     path="/api/register/email",
     *     tags={"Authentication"},
     *     summary="Gửi mã xác thực email",
     *     description="Bước 1: <PERSON><PERSON>i mã xác thực 6 số về email để đăng ký tài khoản",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"email"},
     *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>", description="Email đăng ký")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Gửi mã thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Mã xác thực đã được gửi về email")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Email đã tồn tại hoặc không hợp lệ",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Email đã được đăng ký"),
     *             @OA\Property(property="errors", type="object")
     *         )
     *     )
     * )
     */
    public function sendVerificationCode(Request $request)
    {
        $request->validate([
            'email' => 'required|email|unique:accounts,email',
        ]);
        $email = $request->email;
        $code = rand(100000, 999999);
        $expire = now()->addMinutes(10);
        // Lưu vào cache
        $cacheKey = 'register_' . md5($email);
        Cache::put($cacheKey, [
            'email' => $email,
            'code' => $code,
            'expire' => $expire->toDateTimeString(),
            'verified' => false,
        ], 600); // 10 phút
        // Gửi email (dùng mail mặc định, bạn có thể custom lại)
        try {
            Mail::to($email)->send(new \App\Mail\RegisterVerificationCode($code, $email));
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Không gửi được email. Vui lòng thử lại.'], 500);
        }
        return response()->json(['success' => true]);
    }

    /**
     * @OA\Post(
     *     path="/api/register/verify",
     *     tags={"Authentication"},
     *     summary="Xác thực mã email",
     *     description="Bước 2: Xác thực mã 6 số đã gửi về email",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"email", "code"},
     *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *             @OA\Property(property="code", type="string", example="123456", description="Mã xác thực 6 số")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Xác thực thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Xác thực thành công")
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Mã không đúng hoặc đã hết hạn",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Mã xác thực không đúng hoặc đã hết hạn")
     *         )
     *     )
     * )
     */
    public function checkVerificationCode(Request $request)
    {
        $request->validate([
            'code' => 'required',
            'email' => 'required|email',
        ]);
        $email = $request->email;
        $code = $request->code;
        $cacheKey = 'register_' . md5($email);
        $data = Cache::get($cacheKey);
        if (!$data) {
            return response()->json(['success' => false, 'message' => 'Mã xác thực đã hết hạn hoặc không tồn tại.'], 400);
        }
        $expire = Carbon::parse($data['expire']);
        if (now()->gt($expire)) {
            Cache::forget($cacheKey);
            return response()->json(['success' => false, 'message' => 'Mã xác thực đã hết hạn. Vui lòng lấy lại mã mới.'], 400);
        }
        if ($code != $data['code']) {
            return response()->json(['success' => false, 'message' => 'Mã xác thực không đúng.'], 400);
        }
        // Đánh dấu đã xác thực thành công
        $data['verified'] = true;
        $ttl = now()->diffInSeconds($expire);
        if ($ttl <= 0) {
            Cache::forget($cacheKey);
            return response()->json(['success' => false, 'message' => 'Mã xác thực đã hết hạn. Vui lòng lấy lại mã mới.'], 400);
        }
        $data['verified'] = true;
        Cache::put($cacheKey, $data, $ttl);
        return response()->json(['success' => true]);
    }

    /**
     * @OA\Post(
     *     path="/api/register/complete",
     *     tags={"Authentication"},
     *     summary="Hoàn tất đăng ký",
     *     description="Bước 3: Hoàn tất đăng ký tài khoản với thông tin cá nhân",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"email", "password", "confirm_password", "full_name"},
     *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *             @OA\Property(property="password", type="string", example="123456", description="Mật khẩu"),
     *             @OA\Property(property="confirm_password", type="string", example="123456", description="Xác nhận mật khẩu"),
     *             @OA\Property(property="full_name", type="string", example="Nguyễn Văn A", description="Họ và tên")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Đăng ký thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Đăng ký thành công"),
     *             @OA\Property(property="user", type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="email", type="string", example="<EMAIL>"),
     *                 @OA\Property(property="username", type="string", example="user"),
     *                 @OA\Property(property="full_name", type="string", example="Nguyễn Văn A")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Chưa xác thực email hoặc dữ liệu không hợp lệ",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Vui lòng xác thực email trước")
     *         )
     *     )
     * )
     */
    public function completeRegistration(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'username' => 'required|string|max:50|unique:accounts,username',
            'full_name' => 'required|string|max:255',
            'password' => 'required|string|min:6|confirmed',
        ]);
        $email = $request->email;
        $cacheKey = 'register_' . md5($email);
        $data = Cache::get($cacheKey);
        if (!$data || empty($data['verified'])) {
            return response()->json(['success' => false, 'message' => 'Bạn chưa xác thực email hoặc mã đã hết hạn.'], 400);
        }
        // Tạo tài khoản
        $account = Account::create([
            'username' => $request->username,
            'full_name' => $request->full_name,
            'email' => $email,
            'password' => Hash::make($request->password),
            'email_verified_at' => now()
        ]);
        Auth::login($account);
        // Xóa cache đăng ký
        Cache::forget($cacheKey);
        return response()->json(['success' => true, 'redirect' => route('dashboard', ['locale' => app()->getLocale()])]);
    }
}
