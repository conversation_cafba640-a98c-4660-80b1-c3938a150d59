<x-guest-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ trans_db('general.thong_ke_hoat_dong') ?? 'Thống kê hoạt động' }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="flex min-h-[400px]">
                    <!-- Sidebar -->
                    @include('dashboard.sidebar')

                    <!-- Main Content -->
                    <div class="w-3/4 p-6">
                        <!-- Thống kê tổng quan -->
                        <div class="mb-8">
                            <h3 class="text-xl font-bold mb-4 text-gray-800">{{ trans_db('general.thong_ke_tong_quan') ?? 'Thống kê tổng quan' }}</h3>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div class="bg-blue-50 p-4 rounded-lg border">
                                    <div class="text-2xl font-bold text-blue-600">{{ $personalStats['total_reports'] }}</div>
                                    <div class="text-sm text-gray-600">{{ trans_db('general.tong_bao_cao') ?? 'Tổng báo cáo' }}</div>
                                </div>
                                <div class="bg-green-50 p-4 rounded-lg border">
                                    <div class="text-2xl font-bold text-green-600">{{ $personalStats['approved_reports'] }}</div>
                                    <div class="text-sm text-gray-600">{{ trans_db('general.bao_cao_da_duyet') ?? 'Báo cáo đã duyệt' }}</div>
                                </div>
                                <div class="bg-yellow-50 p-4 rounded-lg border">
                                    <div class="text-2xl font-bold text-yellow-600">{{ $personalStats['total_redemptions'] }}</div>
                                    <div class="text-sm text-gray-600">{{ trans_db('general.lan_doi_qua') ?? 'Lần đổi quà' }}</div>
                                </div>
                                <div class="bg-purple-50 p-4 rounded-lg border">
                                    <div class="text-2xl font-bold text-purple-600">{{ number_format($personalStats['total_points_earned']) }}</div>
                                    <div class="text-sm text-gray-600">{{ trans_db('general.diem_da_tich') ?? 'Điểm đã tích' }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Thống kê chi tiết -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                            <!-- Thống kê báo cáo -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="font-bold text-lg mb-4 text-gray-800">{{ trans_db('general.thong_ke_bao_cao') ?? 'Thống kê báo cáo' }}</h4>
                                <div class="space-y-3">
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">{{ trans_db('general.da_duyet') ?? 'Đã duyệt' }}</span>
                                        <span class="font-bold text-green-600">{{ $personalStats['approved_reports'] }}</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">{{ trans_db('general.dang_cho') ?? 'Đang chờ' }}</span>
                                        <span class="font-bold text-yellow-600">{{ $personalStats['pending_reports'] }}</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">{{ trans_db('general.bi_tu_choi') ?? 'Bị từ chối' }}</span>
                                        <span class="font-bold text-red-600">{{ $personalStats['rejected_reports'] }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Thống kê đổi quà -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="font-bold text-lg mb-4 text-gray-800">{{ trans_db('general.thong_ke_doi_qua') ?? 'Thống kê đổi quà' }}</h4>
                                <div class="space-y-3">
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">{{ trans_db('general.tong_lan_doi') ?? 'Tổng lần đổi' }}</span>
                                        <span class="font-bold text-blue-600">{{ $personalStats['total_redemptions'] }}</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">{{ trans_db('general.da_duyet') ?? 'Đã duyệt' }}</span>
                                        <span class="font-bold text-green-600">{{ $personalStats['approved_redemptions'] }}</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">{{ trans_db('general.da_hoan_thanh') ?? 'Đã hoàn thành' }}</span>
                                        <span class="font-bold text-purple-600">{{ $personalStats['completed_redemptions'] }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Lịch sử tích điểm -->
                        <div class="mb-8">
                            <h3 class="text-xl font-bold mb-4 text-gray-800">{{ trans_db('general.lich_su_tich_diem_gan_day') ?? 'Lịch sử tích điểm gần đây' }}</h3>
                            <div class="bg-gray-50 rounded-lg p-4">
                                @if($pointHistory->count() > 0)
                                    <div class="space-y-3">
                                        @foreach($pointHistory as $history)
                                            <div class="flex justify-between items-center p-3 bg-white rounded border">
                                                <div>
                                                    <p class="font-medium">{{ $history->description }}</p>
                                                    <p class="text-sm text-gray-600">{{ $history->created_at->format('d/m/Y H:i') }}</p>
                                                </div>
                                                <div class="text-right">
                                                    <span class="font-bold {{ $history->change > 0 ? 'text-green-600' : 'text-red-600' }}">
                                                        {{ $history->change > 0 ? '+' : '' }}{{ $history->change }} điểm
                                                    </span>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                @else
                                    <p class="text-gray-500 text-center py-4">{{ trans_db('common.chua_co_lich_su_tich_diem') ?? 'Chưa có lịch sử tích điểm' }}</p>
                                @endif
                            </div>
                        </div>

                        <!-- Lịch sử báo cáo -->
                        <div class="mb-8">
                            <h3 class="text-xl font-bold mb-4 text-gray-800">{{ trans_db('general.lich_su_bao_cao_gan_day') ?? 'Lịch sử báo cáo gần đây' }}</h3>
                            <div class="bg-gray-50 rounded-lg p-4">
                                @if($reportHistory->count() > 0)
                                    <div class="space-y-3">
                                        @foreach($reportHistory as $report)
                                            <div class="flex justify-between items-center p-3 bg-white rounded border">
                                                <div>
                                                    <p class="font-medium">{{ Str::limit($report->description, 100) }}</p>
                                                    <p class="text-sm text-gray-600">{{ $report->created_at->format('d/m/Y H:i') }}</p>
                                                </div>
                                                <div class="text-right">
                                                    <span class="px-2 py-1 rounded text-xs font-medium
                                                        @if($report->status === 'approved') bg-green-100 text-green-800
                                                        @elseif($report->status === 'pending') bg-yellow-100 text-yellow-800
                                                        @else bg-red-100 text-red-800 @endif">
                                                        {{ ucfirst($report->status) }}
                                                    </span>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                @else
                                    <p class="text-gray-500 text-center py-4">{{ trans_db('common.chua_co_lich_su_bao_cao') ?? 'Chưa có lịch sử báo cáo' }}</p>
                                @endif
                            </div>
                        </div>

                        <!-- Lịch sử đổi quà -->
                        <div class="mb-8">
                            <h3 class="text-xl font-bold mb-4 text-gray-800">{{ trans_db('general.lich_su_doi_qua_gan_day') ?? 'Lịch sử đổi quà gần đây' }}</h3>
                            <div class="bg-gray-50 rounded-lg p-4">
                                @if($redemptionHistory->count() > 0)
                                    <div class="space-y-3">
                                        @foreach($redemptionHistory as $redemption)
                                            <div class="flex justify-between items-center p-3 bg-white rounded border">
                                                <div>
                                                    <p class="font-medium">{{ $redemption->gift->name ?? trans_db('general.qua_tang') ?? 'Quà tặng' }}</p>
                                                    <p class="text-sm text-gray-600">{{ $redemption->created_at->format('d/m/Y H:i') }}</p>
                                                    <p class="text-sm text-gray-500">{{ trans_db('general.so_luong') ?? 'Số lượng' }}: {{ $redemption->quantity }}</p>
                                                </div>
                                                <div class="text-right">
                                                    <span class="font-bold text-red-600">-{{ $redemption->point_used }} {{ trans_db('general.diem') ?? 'điểm' }}</span>
                                                    <div class="text-sm text-gray-600">{{ ucfirst($redemption->status) }}</div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                @else
                                    <p class="text-gray-500 text-center py-4">{{ trans_db('common.chua_co_lich_su_doi_qua') ?? 'Chưa có lịch sử đổi quà' }}</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-guest-layout>