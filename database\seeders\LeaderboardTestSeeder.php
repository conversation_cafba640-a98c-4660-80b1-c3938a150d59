<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Account;
use App\Models\PointHistory;
use Illuminate\Support\Facades\Hash;
use Faker\Factory as Faker;

class LeaderboardTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faker = Faker::create();

        // Create 20 test accounts with varying points
        $pointDistributions = [
            // Top tier (1500-3000 points)
            [2850, 2750, 2600, 2450, 2300],
            // High tier (800-1499 points)  
            [1400, 1250, 1100, 950, 800],
            // Mid tier (300-799 points)
            [750, 650, 550, 450, 350],
            // Low tier (50-299 points)
            [250, 200, 150, 100, 50]
        ];

        $allPoints = array_merge(...$pointDistributions);

        foreach ($allPoints as $index => $points) {
            $account = Account::create([
                'username' => 'testuser' . str_pad($index + 1, 2, '0', STR_PAD_LEFT),
                'email' => 'testuser' . ($index + 1) . '@example.com',
                'phone_number' => $faker->phoneNumber,
                'full_name' => $faker->name,
                'password' => Hash::make('password123'),
                'address' => $faker->address,
                'avatar' => null,
                'point' => $points,
                'is_active' => true,
                'email_verified_at' => now(),
            ]);

            // Create point history entries to simulate earning points
            $remainingPoints = $points;
            $historyCount = rand(3, 8); // Random number of point earning events

            for ($i = 0; $i < $historyCount && $remainingPoints > 0; $i++) {
                $pointsToAdd = $i === $historyCount - 1 
                    ? $remainingPoints // Last entry gets remaining points
                    : rand(min(10, $remainingPoints), min(100, $remainingPoints));

                $descriptions = [
                    'Cộng điểm khi đăng nhập',
                    'Cộng điểm khi báo cáo được xác nhận: Rác thải tại công viên',
                    'Cộng điểm khi báo cáo được xác nhận: Rác thải tại bãi biển',
                    'Cộng điểm khi báo cáo được xác nhận: Rác thải tại đường phố',
                    'Cộng điểm hoạt động tích cực',
                    'Cộng điểm tham gia sự kiện',
                ];

                PointHistory::create([
                    'account_id' => $account->id,
                    'change' => $pointsToAdd,
                    'description' => $descriptions[array_rand($descriptions)],
                    'created_at' => $faker->dateTimeBetween('-3 months', 'now'),
                ]);

                $remainingPoints -= $pointsToAdd;
            }
        }

        $this->command->info('Created ' . count($allPoints) . ' test accounts with leaderboard data');
    }
} 