[PHP]
; Minimal PHP configuration to avoid extension loading errors
; Disable problematic extensions temporarily

; Core settings
memory_limit = 256M
max_execution_time = 300
upload_max_filesize = 10M
post_max_size = 10M

; Enable only essential extensions
extension_dir = "D:\xampp\php\ext"

; Essential extensions for <PERSON><PERSON> (comment out problematic ones)
; extension=bz2
; extension=curl
; extension=fileinfo
; extension=gd
; extension=gettext
; extension=intl
; extension=mbstring
; extension=exif
; extension=mysqli
; extension=pdo_mysql
extension=pdo_sqlite
; extension=zip
; extension=openssl

; Disable browscap to avoid file not found error
; browscap = "D:\xampp\php\extras\browscap.ini"
