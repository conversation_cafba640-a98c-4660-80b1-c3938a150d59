<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Checkin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Event;
use App\Models\EventRegistration;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class ApiEventController extends Controller
{

    /**
     * @OA\Get(
     *     path="/api/app/events",
     *     tags={"Events"},
     *     summary="Lấy danh sách sự kiện",
     *     description="L<PERSON>y danh sách sự kiện, có phân trang. Không yêu cầu đăng nhập.",
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Số trang",
     *         required=false,
     *         @OA\Schema(type="integer", default=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Số sự kiện mỗi trang (tối đa 50)",
     *         required=false,
     *         @OA\Schema(type="integer", default=15, maximum=50)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="array",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="title", type="string", example="Sự kiện môi trường"),
     *                     @OA\Property(property="slug", type="string", example="su-kien-moi-truong-abc12"),
     *                     @OA\Property(property="description", type="string", example="Mô tả sự kiện"),
     *                     @OA\Property(property="content", type="string", example="Nội dung chi tiết sự kiện"),
     *                     @OA\Property(property="location", type="string", example="Hà Nội"),
     *                     @OA\Property(property="start_date", type="string", example="2024-12-20 08:00:00"),
     *                     @OA\Property(property="end_date", type="string", example="2024-12-20 17:00:00"),
     *                     @OA\Property(property="featured_image", type="string", nullable=true, example="http://your-domain.com/storage/events/abc.jpg"),
     *                     @OA\Property(property="is_active", type="boolean", example=false),
     *                     @OA\Property(property="max_participants", type="integer", nullable=true, example=100),
     *                     @OA\Property(property="status", type="string", example="pending"),
     *                     @OA\Property(property="created_by", type="integer", example=2),
     *                     @OA\Property(property="joined", type="integer", example=1, description="1: user đã đăng ký, 0: chưa hoặc chưa đăng nhập"),
     *                     @OA\Property(property="checkin", type="integer", example=1, description="1: user đã checkin, 0: chưa checkin")
     *                 )
     *             ),
     *             @OA\Property(property="pagination", type="object",
     *                 @OA\Property(property="current_page", type="integer", example=1),
     *                 @OA\Property(property="per_page", type="integer", example=15),
     *                 @OA\Property(property="total", type="integer", example=20),
     *                 @OA\Property(property="last_page", type="integer", example=2),
     *                 @OA\Property(property="has_more", type="boolean", example=false)
     *             )
     *         )
     *     )
     * )
     */
    public function index(Request $request)
    {
        $query = Event::where('is_active', 1)
            ->where('status', 'approved')
            ->orderByDesc('created_at');
        $page = $request->get('page', 1);
        $perPage = min($request->get('per_page', 15), 50);
        $total = $query->count();
        $events = $query->offset(($page-1)*$perPage)->limit($perPage)->get();
        // Lấy user từ request hoặc từ token thủ công nếu không có middleware sanctum
        $user = $request->user();
        if (!$user) {
            $accessToken = $request->bearerToken();
            if ($accessToken) {
                $token = \Laravel\Sanctum\PersonalAccessToken::findToken($accessToken);
                if ($token) {
                    $user = $token->tokenable;
                }
            }
        }
        $events->transform(function($event) use ($user) {
            $arr = $event->toArray();
            if ($event->featured_image) {
                $arr['featured_image'] = asset('storage/' . $event->featured_image);
            }
            $arr['start_date'] = formatDateTime($event->start_date);
            $arr['end_date'] = formatDateTime($event->end_date);
            $arr['created_at'] = formatDateTime($event->created_at);
            $arr['updated_at'] = formatDateTime($event->updated_at);
            // Kiểm tra user đã join chưa
            $arr['joined'] = 0; // Default to 0
            $arr['checkin'] = 0; // Default to 0
            if ($user) {
                $arr['joined'] = EventRegistration::where('event_id', $event->id)
                    ->where('account_id', $user->id)
                    ->exists() ? 1 : 0;
                $arr['checkin'] = Checkin::where('event_id', $event->id)
                    ->where('account_id', $user->id)
                    ->exists() ? 1 : 0;
            }
            return $arr;
        });
        return response()->json([
            'success' => true,
            'data' => $events,
            'pagination' => [
                'current_page' => (int) $page,
                'per_page' => (int) $perPage,
                'total' => $total,
                'last_page' => ceil($total / $perPage),
                'has_more' => (($page-1)*$perPage + $perPage) < $total,
            ]
        ]);
    }

    public function register(Request $request)
    {
        $request->validate([
            'event_id' => 'required|exists:events,id',
        ]);
        
        $user = $request->user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => 'Bạn cần đăng nhập!'], 401);
        }
        
        $eventId = $request->event_id;
        $event = Event::find($eventId);
        
        // Kiểm tra sự kiện
        if ($event->status !== 'approved') {
            return response()->json(['success' => false, 'message' => 'Sự kiện chưa được phê duyệt!'], 400);
        }
        
        if (!$event->is_active) {
            return response()->json(['success' => false, 'message' => 'Sự kiện không hoạt động!'], 400);
        }
        
        // if ($event->start_date <= now()) {
        //     return response()->json(['success' => false, 'message' => 'Sự kiện đã bắt đầu hoặc kết thúc!'], 400);
        // }
        
        // Kiểm tra đã đăng ký chưa
        $exists = EventRegistration::where('event_id', $eventId)
                                 ->where('account_id', $user->id)
                                 ->exists();
        if ($exists) {
            return response()->json(['success' => false, 'message' => 'Bạn đã đăng ký sự kiện này!'], 400);
        }
        
        // Kiểm tra số lượng tối đa
        if ($event->max_participants) {
            $currentRegistrations = $event->registrations()->count();
            if ($currentRegistrations >= $event->max_participants) {
                return response()->json(['success' => false, 'message' => 'Sự kiện đã đầy!'], 400);
            }
        }
        
        EventRegistration::create([
            'event_id' => $eventId,
            'account_id' => $user->id,
            'registered_at' => now(),
        ]);
        
        return response()->json(['success' => true, 'message' => 'Đăng ký tham gia sự kiện thành công!']);
    }

    /**
     * @OA\Post(
     *     path="/api/app/events/create",
     *     tags={"Events"},
     *     summary="Tạo mới sự kiện",
     *     description="Tạo mới một sự kiện, yêu cầu đăng nhập (sanctum)",
     *     security={{"sanctum": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 required={"title", "description", "content", "location", "start_date", "end_date"},
     *                 @OA\Property(property="title", type="string", example="Sự kiện môi trường"),
     *                 @OA\Property(property="description", type="string", example="Mô tả sự kiện"),
     *                 @OA\Property(property="content", type="string", example="Nội dung chi tiết sự kiện"),
     *                 @OA\Property(property="location", type="string", example="Hà Nội"),
     *                 @OA\Property(property="start_date", type="string", format="date-time", example="2024-12-20 08:00:00"),
     *                 @OA\Property(property="end_date", type="string", format="date-time", example="2024-12-20 17:00:00"),
     *                 @OA\Property(property="featured_image", type="string", format="binary", nullable=true, description="Ảnh sự kiện (jpg, png, jpeg, gif, svg, max 4MB)"),
     *                 @OA\Property(property="max_participants", type="integer", nullable=true, example=100)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Tạo sự kiện thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Tạo sự kiện thành công!"),
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="title", type="string", example="Sự kiện môi trường"),
     *                 @OA\Property(property="slug", type="string", example="su-kien-moi-truong-abc12"),
     *                 @OA\Property(property="description", type="string", example="Mô tả sự kiện"),
     *                 @OA\Property(property="content", type="string", example="Nội dung chi tiết sự kiện"),
     *                 @OA\Property(property="location", type="string", example="Hà Nội"),
     *                 @OA\Property(property="start_date", type="string", example="2024-12-20 08:00:00"),
     *                 @OA\Property(property="end_date", type="string", example="2024-12-20 17:00:00"),
     *                 @OA\Property(property="featured_image", type="string", nullable=true, example="/storage/events/abc.jpg"),
     *                 @OA\Property(property="is_active", type="boolean", example=true),
     *                 @OA\Property(property="max_participants", type="integer", nullable=true, example=100),
     *                 @OA\Property(property="status", type="string", example="active"),
     *                 @OA\Property(property="created_by", type="integer", example=2),
     *                 @OA\Property(property="creator", type="object",
     *                     @OA\Property(property="id", type="integer", example=2),
     *                     @OA\Property(property="username", type="string", example="user123"),
     *                     @OA\Property(property="full_name", type="string", example="Nguyễn Văn A")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Chưa đăng nhập",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Bạn cần đăng nhập!")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Lỗi validate",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="The given data was invalid."),
     *             @OA\Property(property="errors", type="object")
     *         )
     *     )
     * )
     */
    public function create(Request $request)
    {
        $user = $request->user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => 'Bạn cần đăng nhập!'], 401);
        }
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'content' => 'required|string',
            'location' => 'required|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:4096',
            'max_participants' => 'nullable|integer|min:1',
        ]);
        $imagePath = null;
        if ($request->hasFile('featured_image')) {
            $imagePath = $request->file('featured_image')->store('events', 'public');
        }
        $event = \App\Models\Event::create([
            'title' => $request->input('title'),
            'slug' => Str::slug($request->input('title')) . '-' . Str::random(5),
            'description' => $request->input('description'),
            'content' => $request->input('content'),
            'location' => $request->input('location'),
            'start_date' => $request->input('start_date'),
            'end_date' => $request->input('end_date'),
            'featured_image' => $imagePath,
            'is_active' => false,
            'max_participants' => $request->input('max_participants'),
            'status' => 'pending',
            'created_by' => $user->id,
        ]);
        $event->load('creator:id,username,full_name');
        $eventArr = $event->toArray();
        if ($event->featured_image) {
            $eventArr['featured_image'] = asset('storage/' . $event->featured_image);
        }
        $eventArr['start_date'] = formatDateTime($event->start_date);
        $eventArr['end_date'] = formatDateTime($event->end_date);
        $eventArr['created_at'] = formatDateTime($event->created_at);
        $eventArr['updated_at'] = formatDateTime($event->updated_at);
        return response()->json([
            'success' => true,
            'message' => 'Tạo sự kiện thành công!',
            'data' => $eventArr
        ], 201);
    }

    /**
     * @OA\Get(
     *     path="/api/app/events/by-me",
     *     tags={"Events"},
     *     summary="Lấy danh sách sự kiện do tôi tạo",
     *     description="Lấy danh sách sự kiện do user hiện tại tạo, có phân trang. Yêu cầu đăng nhập (sanctum)",
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Số trang",
     *         required=false,
     *         @OA\Schema(type="integer", default=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Số sự kiện mỗi trang (tối đa 50)",
     *         required=false,
     *         @OA\Schema(type="integer", default=15, maximum=50)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="array",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="title", type="string", example="Sự kiện môi trường"),
     *                     @OA\Property(property="slug", type="string", example="su-kien-moi-truong-abc12"),
     *                     @OA\Property(property="description", type="string", example="Mô tả sự kiện"),
     *                     @OA\Property(property="content", type="string", example="Nội dung chi tiết sự kiện"),
     *                     @OA\Property(property="location", type="string", example="Hà Nội"),
     *                     @OA\Property(property="start_date", type="string", example="2024-12-20 08:00:00"),
     *                     @OA\Property(property="end_date", type="string", example="2024-12-20 17:00:00"),
     *                     @OA\Property(property="featured_image", type="string", nullable=true, example="http://your-domain.com/storage/events/abc.jpg"),
     *                     @OA\Property(property="is_active", type="boolean", example=false),
     *                     @OA\Property(property="max_participants", type="integer", nullable=true, example=100),
     *                     @OA\Property(property="status", type="string", example="active"),
     *                     @OA\Property(property="created_by", type="integer", example=2),
     *                     @OA\Property(property="creator", type="object",
     *                         @OA\Property(property="id", type="integer", example=2),
     *                         @OA\Property(property="username", type="string", example="user123"),
     *                         @OA\Property(property="full_name", type="string", example="Nguyễn Văn A")
     *                     ),
     *                     @OA\Property(property="joined", type="integer", example=1, description="1: user đã đăng ký, 0: chưa hoặc chưa đăng nhập")
     *                 )
     *             ),
     *             @OA\Property(property="pagination", type="object",
     *                 @OA\Property(property="current_page", type="integer", example=1),
     *                 @OA\Property(property="per_page", type="integer", example=15),
     *                 @OA\Property(property="total", type="integer", example=20),
     *                 @OA\Property(property="last_page", type="integer", example=2),
     *                 @OA\Property(property="has_more", type="boolean", example=false)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Chưa đăng nhập",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Bạn cần đăng nhập!")
     *         )
     *     )
     * )
     */
    public function byMe(Request $request)
    {
        $user = $request->user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => 'Bạn cần đăng nhập!'], 401);
        }
        $query = Event::with('creator:id,username,full_name')
            ->where('created_by', $user->id)
            ->orderByDesc('created_at');
        $page = $request->get('page', 1);
        $perPage = min($request->get('per_page', 15), 50);
        $total = $query->count();
        $events = $query->offset(($page-1)*$perPage)->limit($perPage)->get();
        // Xử lý url ảnh
        $events->transform(function($event) {
            $arr = $event->toArray();
            if ($event->featured_image) {
                $arr['featured_image'] = asset('storage/' . $event->featured_image);
            }
            $arr['start_date'] = formatDateTime($event->start_date);
            $arr['end_date'] = formatDateTime($event->end_date);
            $arr['created_at'] = formatDateTime($event->created_at);
            $arr['updated_at'] = formatDateTime($event->updated_at);
            return $arr;
        });
        return response()->json([
            'success' => true,
            'data' => $events,
            'pagination' => [
                'current_page' => (int) $page,
                'per_page' => (int) $perPage,
                'total' => $total,
                'last_page' => ceil($total / $perPage),
                'has_more' => (($page-1)*$perPage + $perPage) < $total,
            ]
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/app/events/joined",
     *     tags={"Events"},
     *     summary="Lấy danh sách sự kiện đã tham gia",
     *     description="Lấy danh sách đăng ký sự kiện của user hiện tại, chỉ trả về thông tin đăng ký, tên, slug, địa điểm, thời gian bắt đầu và kết thúc sự kiện.",
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Số trang",
     *         required=false,
     *         @OA\Schema(type="integer", default=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Số bản ghi mỗi trang (tối đa 50)",
     *         required=false,
     *         @OA\Schema(type="integer", default=15, maximum=50)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="array",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="event_id", type="integer", example=5),
     *                     @OA\Property(property="event_title", type="string", example="Sự kiện môi trường"),
     *                     @OA\Property(property="event_slug", type="string", example="su-kien-moi-truong-abc12"),
     *                     @OA\Property(property="event_location", type="string", example="Hà Nội"),
     *                     @OA\Property(property="event_start_date", type="string", example="2024-12-20 08:00:00"),
     *                     @OA\Property(property="event_end_date", type="string", example="2024-12-20 17:00:00"),
     *                     @OA\Property(property="registered_at", type="string", example="2024-12-20 10:30:45")
     *                 )
     *             ),
     *             @OA\Property(property="pagination", type="object",
     *                 @OA\Property(property="current_page", type="integer", example=1),
     *                 @OA\Property(property="per_page", type="integer", example=15),
     *                 @OA\Property(property="total", type="integer", example=20),
     *                 @OA\Property(property="last_page", type="integer", example=2),
     *                 @OA\Property(property="has_more", type="boolean", example=false)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Chưa đăng nhập",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Bạn cần đăng nhập!")
     *         )
     *     )
     * )
     */
    public function eventsJoined(Request $request)
    {
        $user = $request->user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => 'Bạn cần đăng nhập!'], 401);
        }
        $registrationQuery = \App\Models\EventRegistration::with('event:id,title,slug,location,start_date,end_date')->where('account_id', $user->id);
        $total = $registrationQuery->count();
        $page = $request->get('page', 1);
        $perPage = min($request->get('per_page', 15), 50);
        $registrations = $registrationQuery->offset(($page-1)*$perPage)->limit($perPage)->get();
        $data = $registrations->map(function($reg) {
            return [
                'id' => $reg->id,
                'event_id' => $reg->event_id,
                'event_title' => $reg->event ? $reg->event->title : null,
                'event_slug' => $reg->event ? $reg->event->slug : null,
                'event_location' => $reg->event ? $reg->event->location : null,
                'event_start_date' => $reg->event ? formatDateTime($reg->event->start_date) : null,
                'event_end_date' => $reg->event ? formatDateTime($reg->event->end_date) : null,
                'registered_at' => $reg->registered_at ? formatDateTime($reg->registered_at) : null,
            ];
        });
        return response()->json([
            'success' => true,
            'data' => $data,
            'pagination' => [
                'current_page' => (int) $page,
                'per_page' => (int) $perPage,
                'total' => $total,
                'last_page' => ceil($total / $perPage),
                'has_more' => (($page-1)*$perPage + $perPage) < $total,
            ]
        ]);
    }

    /**
     * @OA\Post(
     *     path="/api/app/events/register-event",
     *     tags={"Events"},
     *     summary="Đăng ký tham gia sự kiện",
     *     description="Đăng ký tham gia một sự kiện, yêu cầu đăng nhập (sanctum)",
     *     security={{"sanctum": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"event_id"},
     *             @OA\Property(property="event_id", type="integer", example=1, description="ID của sự kiện")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Đăng ký thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Đăng ký tham gia sự kiện thành công!")
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Đã đăng ký hoặc sự kiện không hợp lệ",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Bạn đã đăng ký sự kiện này!")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Chưa đăng nhập",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Bạn cần đăng nhập!")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Sự kiện không tồn tại",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Sự kiện không tồn tại")
     *         )
     *     )
     * )
     */
    public function registerEvent(Request $request)
    {
        $request->validate([
            'event_id' => 'required|exists:events,id',
        ]);
        $user = $request->user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => 'Bạn cần đăng nhập!'], 401);
        }
        $eventId = $request->event_id;
        $event = \App\Models\Event::find($eventId);
        if (!$event) {
            return response()->json(['success' => false, 'message' => 'Sự kiện không tồn tại'], 404);
        }
        if ($event->status !== 'approved') {
            return response()->json(['success' => false, 'message' => 'Sự kiện chưa được phê duyệt!'], 400);
        }
        if (!$event->is_active) {
            return response()->json(['success' => false, 'message' => 'Sự kiện không hoạt động!'], 400);
        }
        // if ($event->start_date <= now()) {
        //     return response()->json(['success' => false, 'message' => 'Sự kiện đã bắt đầu hoặc kết thúc!'], 400);
        // }
        $exists = \App\Models\EventRegistration::where('event_id', $eventId)
            ->where('account_id', $user->id)
            ->exists();
        if ($exists) {
            return response()->json(['success' => false, 'message' => 'Bạn đã đăng ký sự kiện này!'], 400);
        }
        if ($event->max_participants) {
            $currentRegistrations = $event->registrations()->count();
            if ($currentRegistrations >= $event->max_participants) {
                return response()->json(['success' => false, 'message' => 'Sự kiện đã đầy!'], 400);
            }
        }
        \App\Models\EventRegistration::create([
            'event_id' => $eventId,
            'account_id' => $user->id,
            'registered_at' => now(),
        ]);
        return response()->json(['success' => true, 'message' => 'Đăng ký tham gia sự kiện thành công!']);
    }

     /**
     * @OA\Post(
     *     path="/api/app/events/checkin",
     *     tags={"Events"},
     *     summary="Check-in sự kiện",
     *     description="User gửi yêu cầu check-in sự kiện, status mặc định là pending. Yêu cầu đăng nhập (sanctum)",
     *     security={{"sanctum": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"event_id", "lat", "long"},
     *             @OA\Property(property="event_id", type="integer", example=1),
     *             @OA\Property(property="lat", type="number", format="float", example=21.0285),
     *             @OA\Property(property="long", type="number", format="float", example=105.8542)
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Check-in thành công (đang chờ duyệt)",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="user_id", type="integer", example=2),
     *                 @OA\Property(property="event_id", type="integer", example=1),
     *                 @OA\Property(property="lat", type="number", example=21.0285),
     *                 @OA\Property(property="long", type="number", example=105.8542),
     *                 @OA\Property(property="status", type="string", example="pending"),
     *                 @OA\Property(property="created_at", type="string", example="2024-07-01 10:00:00")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Chưa đăng nhập",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Bạn cần đăng nhập!")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Lỗi validate",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="The given data was invalid."),
     *             @OA\Property(property="errors", type="object")
     *         )
     *     )
     * )
     */
    public function eventCheckin(Request $request)
    {
        $user = $request->user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => 'Bạn cần đăng nhập!'], 401);
        }
        $request->validate([
            'event_id' => 'required|exists:events,id',
            'lat' => 'required|numeric',
            'long' => 'required|numeric',
        ]);
        if (Checkin::where('account_id', $user->id)->where('event_id', $request->input('event_id'))->exists()) {
            return response()->json(['success' => false, 'message' => 'Bạn đã check-in sự kiện này!'], 400);
        }
        $checkin = Checkin::create([
            'account_id' => $user->id,
            'event_id' => $request->input('event_id'),
            'lat' => $request->input('lat'),
            'long' => $request->input('long'),
            'status' => 'pending',
        ]);
        return response()->json([
            'success' => true,
            'data' => $checkin,
            'message' => 'Check-in thành công (đang chờ duyệt)',
        ], 201);
    }
} 