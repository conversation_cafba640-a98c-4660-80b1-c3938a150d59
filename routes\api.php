<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\ApiRegisterController;
use App\Http\Controllers\Api\ApiProfileController;
use App\Http\Controllers\Api\ApiAuthController;
use App\Http\Controllers\Api\ApiReportController;
use App\Http\Controllers\Api\ApiEventController;
use App\Http\Controllers\Api\ApiForumController;
use App\Http\Controllers\Api\ApiGiftController;
use App\Http\Controllers\GiftController;
use App\Http\Controllers\Api\ApiLeaderboardController;

use App\Http\Controllers\Api\ApiMapController;
use App\Http\Controllers\Api\ApiNotificationController;
use App\Http\Controllers\Api\ApiPointController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// ============== WEB-BASED API ROUTES (Session Authentication) ==============
// Đây là các routes cho web app, vẫn dùng session
Route::middleware(['web'])->group(function () {
    Route::post('register/email', [ApiRegisterController::class, 'sendVerificationCode']);
    Route::post('register/verify', [ApiRegisterController::class, 'checkVerificationCode']);
    Route::post('register/complete', [ApiRegisterController::class, 'completeRegistration']);
});
Route::middleware(['web', 'auth:account'])->post('/reports', [ApiReportController::class, 'store']);

Route::middleware(['web', 'auth:account'])->post('/logout', [ApiAuthController::class, 'logout'])->name('web.logout');
Route::middleware(['web', 'auth:account'])->post('/profile/update', [ApiProfileController::class, 'update']);
Route::middleware(['web', 'auth:account'])->post('/event/register', [ApiEventController::class, 'register']);
Route::middleware(['web', 'auth:account'])->post('/forum/create', [ApiForumController::class, 'store']);
Route::middleware(['web', 'auth:account'])->post('/forum/reply', [ApiForumController::class, 'reply']);

// Gift routes
Route::middleware(['web', 'auth:account'])->post('/gifts/redeem', [GiftController::class, 'redeem'])->middleware(['auth:account', 'verified'])->name('gifts.redeem');
Route::middleware(['web', 'auth:account'])->get('/gifts/history', [GiftController::class, 'history'])->middleware(['auth:account', 'verified'])->name('gifts.history');



Route::group(['prefix' => 'app'], function () {
    // Group routes APP
    Route::post('/login', [ApiAuthController::class, 'login']);
    Route::post('/register', [ApiAuthController::class, 'appRegister']);
    Route::post('/forgot-password', [ApiAuthController::class, 'appForgotPassword']);
    Route::get('/map', [ApiMapController::class, 'appMap']);

    Route::get('/gifts', [ApiGiftController::class, 'index']);


    // Leaderboard routes (public access)
    Route::group(['prefix' => 'leaderboard'], function () {
        Route::get('/top', [ApiLeaderboardController::class, 'topMembers']);
    });

    // Additional forum routes
    Route::group(['prefix' => 'forum'], function () {
        Route::get('/user/threads', [ApiForumController::class, 'userThreads']);
        Route::get('/{slug}/replies', [ApiForumController::class, 'childReplies']);
    });

    // Forum API Resource
    Route::apiResource('forum', ApiForumController::class)->parameters(['forum' => 'slug'])->names([
        'index' => 'api.forum.index',
        'show' => 'api.forum.show',
        'store' => 'api.forum.store',
        'update' => 'api.forum.update',
        'destroy' => 'api.forum.destroy'
    ]);
    Route::apiResource('events', ApiEventController::class)->only(['index'])->names([
        'index' => 'api.events.index'
    ]);

    Route::group(['middleware' => 'auth:sanctum'], function () {

        Route::post('logout', [ApiAuthController::class, 'logout']);
        Route::post('refresh-token', [ApiAuthController::class, 'refreshToken']);

        Route::prefix('profile')->group(function () {
            Route::get('me', [ApiProfileController::class, 'getUserInfo']);
            Route::get('stats', [ApiProfileController::class, 'getUserStats']);
            Route::post('update-profile', [ApiProfileController::class, 'updateProfile']);
        });

        Route::group(['prefix' => 'leaderboard'], function () {
            Route::get('/member/{accountId}', [ApiLeaderboardController::class, 'getMemberRank']);
        });

        Route::group(['prefix' => 'forum'], function () {
            Route::post('/create/threads', [ApiForumController::class, 'createThreads']);
            Route::post('/reply', [ApiForumController::class, 'reply']);
            Route::put('/reply/{replyId}', [ApiForumController::class, 'updateReply']);
            Route::delete('/reply/{replyId}', [ApiForumController::class, 'destroyReply']);
        });

        // Event routes
        Route::group(['prefix' => 'events'], function () {
            Route::post('register', [ApiEventController::class, 'register']);
            Route::post('create', [ApiEventController::class, 'create']);
            Route::get('by-me', [ApiEventController::class, 'byMe']);
            Route::get('joined', [ApiEventController::class, 'eventsJoined']);
            Route::post('register-event', [ApiEventController::class, 'registerEvent']);
            Route::post('/checkin', [ApiEventController::class, 'eventCheckin']);
        });

        Route::prefix('leaderboard')->group(function () {
            Route::get('top-members', [ApiLeaderboardController::class, 'topMembers']);
        });

        Route::prefix('reports')->group(function () {
            Route::post('history', [ApiReportController::class, 'history']);
            Route::post('create-report', [ApiReportController::class, 'createReport']);
            Route::post('stats', [ApiReportController::class, 'stats']);
        });

        Route::prefix('gifts')->group(function () {
            Route::post('redeem', [ApiGiftController::class, 'redeem']);
            Route::post('history', [ApiGiftController::class, 'history']);
        });

        Route::prefix('points')->group(function () {
            Route::post('history-points-add', [ApiPointController::class, 'historyPointsAdd']);
        });

        // Notification routes
        Route::group(['prefix' => 'notifications'], function () {
            Route::post('/', [ApiNotificationController::class, 'index']);
            Route::post('/{id}/read', [ApiNotificationController::class, 'markAsRead']);
            Route::post('/has-unread', [ApiNotificationController::class, 'hasUnread']);
        });
    });

});
