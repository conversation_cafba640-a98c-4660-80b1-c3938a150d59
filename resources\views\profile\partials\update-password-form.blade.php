<section>
    <header>
        <h2 class="text-lg font-medium text-gray-900">
            {{ __('Update Password') }}
        </h2>

        <p class="mt-1 text-sm text-gray-600">
            {{ __('Ensure your account is using a long, random password to stay secure.') }}
        </p>
    </header>

    <form method="post('password.update') }}" class="mt-6 space-y-6">
        @csrf
        @method('put')

        <div>
            <x-input-label for="update_password_current_password('Current Password')" />
            <x-text-input id="update_password_current_password" autocomplete="current-password('current_password')" class="mt-2" />
        </div>

        <div>
            <x-input-label for="update_password_password('New Password')" />
            <x-text-input id="update_password_password" autocomplete="new-password('password')" class="mt-2" />
        </div>

        <div>
            <x-input-label for="update_password_password_confirmation('Confirm Password')" />
            <x-text-input id="update_password_password_confirmation" autocomplete="new-password('password_confirmation')" class="mt-2" />
        </div>

        <div class="flex items-center gap-4">
            <x-primary-button>{{ __('Save') }}</x-primary-button>

            @if (session('status') === 'password-updated')
                <p
                    x-data="{ show: true }"
                    x-show="show(() => show = false, 2000)"
                    class="text-sm text-gray-600"
                >{{ __('Saved.') }}</p>
            @endif
        </div>
    </form>
</section>
