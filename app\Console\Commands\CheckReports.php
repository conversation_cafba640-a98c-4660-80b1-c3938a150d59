<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Report;

class CheckReports extends Command
{
    protected $signature = 'reports:check';
    protected $description = 'Check all reports in database';

    public function handle()
    {
        $reports = Report::all();
        
        $this->info("Tổng số báo cáo: " . $reports->count());
        $this->info("Báo cáo đã duyệt: " . $reports->where('status', 'approved')->count());
        $this->info("Báo cáo chờ duyệt: " . $reports->where('status', 'pending')->count());
        $this->info("Báo cáo bị từ chối: " . $reports->where('status', 'rejected')->count());
        
        $this->info("\nDanh sách các điểm đổ rác:");
        foreach ($reports as $index => $report) {
            $this->line(($index + 1) . ". " . substr($report->description, 0, 80) . "...");
            $this->line("   Tọa độ: {$report->latitude}, {$report->longitude} - Status: {$report->status}");
        }
        
        return 0;
    }
}
