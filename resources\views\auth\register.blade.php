<x-guest-layout>
    <div class="min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-[#0EA5E9]/10 via-[#10B981]/10 to-[#4ADE80]/10 py-12 px-4">
        <div class="w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 md:p-10">
            <h2 class="text-3xl font-bold text-center mb-8 bg-gradient-to-r from-[#10B981] to-[#0EA5E9] bg-clip-text text-transparent">
                {{ trans_db('nav.dang_ky') ?? '<PERSON><PERSON>ng ký tài khoản' }}
            </h2>

            <!-- Bước 1: Nhập email -->
            <form id="register-step-email" class="space-y-6">
                @csrf
                <div>
                    <x-input-label for="reg_email" :value="trans_db('form.email') ?? 'Email'" class="text-[#10B981] font-semibold" />
                    <x-text-input id="reg_email"
                        class="block mt-1 w-full rounded-lg border border-[#10B981]/30 focus:border-[#10B981] focus:ring-[#10B981]"
                        type="email" name="email" required autofocus
                        placeholder="{{ trans_db('form.nhap_email') ?? 'Nhập email' }}" />
                </div>

                <div>
                    <button type="submit"
                        class="w-full py-3 rounded-lg text-white font-semibold bg-gradient-to-r from-[#10B981] to-[#0EA5E9] hover:from-[#0EA5E9] hover:to-[#10B981] transition duration-300">
                        {{ trans_db('action.gui_ma_xac_thuc') ?? 'Gửi mã xác thực' }}
                    </button>
                </div>
            </form>

            <!-- Bước 2: Nhập mã xác thực -->
            <form id="register-step-code" class="space-y-6" style="display: none;">
                @csrf
                <div>
                    <x-input-label for="reg_code" :value="trans_db('form.ma_xac_thuc') ?? 'Mã xác thực'" class="text-[#10B981] font-semibold" />
                    <x-text-input id="reg_code"
                        class="block mt-1 w-full rounded-lg border border-[#10B981]/30 focus:border-[#10B981] focus:ring-[#10B981] text-center text-2xl tracking-widest"
                        type="text" name="code" required maxlength="6"
                        placeholder="000000" />
                    <p class="text-sm text-gray-600 mt-2">{{ trans_db('form.nhap_ma_6_chu_so_da_gui_ve_email') ?? 'Nhập mã 6 chữ số đã gửi về email của bạn' }}</p>
                </div>

                <div>
                    <button type="submit"
                        class="w-full py-3 rounded-lg text-white font-semibold bg-gradient-to-r from-[#10B981] to-[#0EA5E9] hover:from-[#0EA5E9] hover:to-[#10B981] transition duration-300">
                        {{ trans_db('action.xac_thuc') ?? 'Xác thực' }}
                    </button>
                </div>
            </form>

            <!-- Bước 3: Nhập thông tin tài khoản -->
            <form id="register-step-password" class="space-y-6" style="display: none;">
                @csrf

                <!-- Username -->
                <div>
                    <x-input-label for="reg_username" :value="trans_db('nav.ten_dang_nhap') ?? 'Tên đăng nhập'" class="text-[#10B981] font-semibold" />
                    <x-text-input id="reg_username"
                        class="block mt-1 w-full rounded-lg border border-[#10B981]/30 focus:border-[#10B981] focus:ring-[#10B981]"
                        type="text" name="username" required
                        placeholder="{{ trans_db('nav.nhap_ten_dang_nhap') ?? 'Nhập tên đăng nhập' }}" />
                </div>

                <!-- Full Name -->
                <div>
                    <x-input-label for="reg_full_name" :value="trans_db('form.ho_va_ten') ?? 'Họ và tên'" class="text-[#10B981] font-semibold" />
                    <x-text-input id="reg_full_name"
                        class="block mt-1 w-full rounded-lg border border-[#10B981]/30 focus:border-[#10B981] focus:ring-[#10B981]"
                        type="text" name="full_name" required
                        placeholder="{{ trans_db('form.nhap_ho_va_ten') ?? 'Nhập họ và tên' }}" />
                </div>

                <!-- Password -->
                <div>
                    <x-input-label for="reg_password" :value="trans_db('form.mat_khau') ?? 'Mật khẩu'" class="text-[#10B981] font-semibold" />
                    <x-text-input id="reg_password"
                        class="block mt-1 w-full rounded-lg border border-[#10B981]/30 focus:border-[#10B981] focus:ring-[#10B981]"
                        type="password" name="password" required autocomplete="new-password"
                        placeholder="{{ trans_db('form.nhap_mat_khau') ?? 'Nhập mật khẩu (tối thiểu 6 ký tự)' }}" />
                </div>

                <!-- Confirm Password -->
                <div>
                    <x-input-label for="reg_password_confirmation" :value="trans_db('form.confirm_password') ?? 'Xác nhận mật khẩu'" class="text-[#10B981] font-semibold" />
                    <x-text-input id="reg_password_confirmation"
                        class="block mt-1 w-full rounded-lg border border-[#10B981]/30 focus:border-[#10B981] focus:ring-[#10B981]"
                        type="password" name="password_confirmation" required autocomplete="new-password"
                        placeholder="{{ trans_db('form.nhap_lai_mat_khau') ?? 'Nhập lại mật khẩu' }}" />
                </div>

                <!-- Submit Button -->
                <div>
                    <button type="submit"
                        class="w-full py-3 rounded-lg text-white font-semibold bg-gradient-to-r from-[#10B981] to-[#0EA5E9] hover:from-[#0EA5E9] hover:to-[#10B981] transition duration-300">
                        {{ trans_db('action.tao_tai_khoan') ?? 'Tạo tài khoản' }}
                    </button>
                </div>
            </form>

            <!-- Login Link -->
            <div class="text-center mt-6">
                <p class="text-gray-600">
                    {{ trans_db('nav.da_co_tai_khoan') ?? 'Đã có tài khoản?' }}
                    <a href="{{ route('login', ['locale' => app()->getLocale()]) }}" class="text-[#10B981] hover:text-[#0EA5E9] font-semibold transition duration-300">
                        {{ trans_db('nav.dang_nhap') ?? 'Đăng nhập' }}
                    </a>
                </p>
            </div>
        </div>
    </div>
</x-guest-layout>
