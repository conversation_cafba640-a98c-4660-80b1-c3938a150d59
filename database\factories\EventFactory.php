<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Event>
 */
class EventFactory extends Factory
{
    public function definition(): array
    {
        // Danh sách sự kiện môi trường thực tế
        $environmentalEvents = [
            [
                'title' => 'Chiến dịch dọn rác bãi biển Vũng Tàu',
                'description' => 'Tham gia cùng chúng tôi làm sạch bãi biển Vũng Tàu, bảo vệ hệ sinh thái biển và tạo môi trường du lịch xanh sạch đẹp.',
                'content' => 'Bãi biển Vũng Tàu là một trong những điểm du lịch nổi tiếng của Việt Nam, thu hút hàng triệu lượt khách mỗi năm. <PERSON><PERSON> nhi<PERSON>, lượng rác thải ngày càng tăng đang đe dọa vẻ đẹp tự nhiên và hệ sinh thái biển nơi đây. Chiến dịch này nhằm mục đích thu gom rác thải, phân loại tái chế và tuyên truyền ý thức bảo vệ môi trường cho du khách và người dân địa phương. Chúng tôi sẽ tập trung vào các khu vực bãi biển chính, làm sạch cát bãi, thu gom rác nhựa và các vật liệu có thể tái chế. Hoạt động này không chỉ giúp làm đẹp bãi biển mà còn nâng cao nhận thức về tầm quan trọng của việc bảo vệ môi trường biển.',
                'location' => 'Bãi biển Thùy Vân, Vũng Tàu, Bà Rịa - Vũng Tàu',
                'image' => 'events/beach-cleanup-vungtau.jpg'
            ],
            [
                'title' => 'Ngày hội trồng cây xanh tại Công viên Tao Đàn',
                'description' => 'Cùng nhau trồng cây xanh, tạo không gian xanh mát cho thành phố và góp phần cải thiện chất lượng không khí.',
                'content' => 'Công viên Tao Đàn là "lá phổi xanh" giữa lòng Sài Gòn, nơi người dân thường đến thể dục, thư giãn và tận hưởng không khí trong lành. Để duy trì và phát triển không gian xanh này, chúng tôi tổ chức ngày hội trồng cây với sự tham gia của cộng đồng. Các loại cây được chọn lọc kỹ càng, phù hợp với khí hậu nhiệt đới và có khả năng hấp thụ khí CO2 cao như cây bàng, cây phượng vĩ, cây sao đen. Mỗi người tham gia sẽ được hướng dẫn cách trồng cây đúng kỹ thuật và cam kết chăm sóc cây trong thời gian đầu. Hoạt động này không chỉ góp phần làm xanh thành phố mà còn giáo dục về tầm quan trọng của cây xanh trong việc chống biến đổi khí hậu.',
                'location' => 'Công viên Tao Đàn, Quận 1, TP.HCM',
                'image' => 'events/tree-planting-taodan.jpg'
            ],
            [
                'title' => 'Hội thảo tái chế rác thải nhựa sáng tạo',
                'description' => 'Học cách biến rác thải nhựa thành những sản phẩm hữu ích, góp phần giảm thiểu ô nhiễm môi trường.',
                'content' => 'Rác thải nhựa đang là một trong những vấn đề môi trường nghiêm trọng nhất hiện nay. Hội thảo này sẽ hướng dẫn các phương pháp tái chế rác thải nhựa thành những sản phẩm hữu ích trong đời sống hàng ngày. Các chuyên gia sẽ chia sẻ kiến thức về phân loại nhựa, quy trình tái chế và các ý tưởng sáng tạo để tái sử dụng. Người tham gia sẽ được thực hành làm các sản phẩm như chậu trồng cây từ chai nhựa, túi xách từ bao bì nhựa, đồ chơi giáo dục từ nắp chai. Hội thảo cũng giới thiệu các mô hình kinh doanh xanh, khuyến khích khởi nghiệp trong lĩnh vực tái chế và bảo vệ môi trường.',
                'location' => 'Trung tâm Văn hóa Quận 3, TP.HCM',
                'image' => 'events/plastic-recycling-workshop.jpg'
            ],
            [
                'title' => 'Làm sạch kênh rạch Quận 4',
                'description' => 'Hoạt động vệ sinh môi trường nước, thu gom rác thải và cải thiện hệ thống thoát nước đô thị.',
                'content' => 'Hệ thống kênh rạch tại Quận 4 đóng vai trò quan trọng trong việc thoát nước và điều hòa khí hậu đô thị. Tuy nhiên, việc xả rác bừa bãi đã làm ô nhiễm nguồn nước và gây tắc nghẽn hệ thống thoát nước. Hoạt động này tập trung vào việc thu gom rác thải nổi, làm sạch bờ kênh và tuyên truyền ý thức bảo vệ môi trường nước cho cộng đồng. Chúng tôi sẽ sử dụng các thiết bị chuyên dụng để thu gom rác, phân loại tái chế và xử lý chất thải. Đồng thời, tổ chức các buổi tuyên truyền cho học sinh và người dân về tác hại của việc xả rác xuống kênh rạch và cách bảo vệ nguồn nước sạch.',
                'location' => 'Kênh Tẻ, Quận 4, TP.HCM',
                'image' => 'events/canal-cleanup-q4.jpg'
            ],
            [
                'title' => 'Chợ phiên sản phẩm tái chế và thân thiện môi trường',
                'description' => 'Triển lãm và bán các sản phẩm tái chế, hữu cơ, thúc đẩy lối sống xanh và tiêu dùng bền vững.',
                'content' => 'Chợ phiên này là nơi quy tụ các sản phẩm thân thiện với môi trường, từ thực phẩm hữu cơ đến đồ dùng tái chế. Các gian hàng sẽ trưng bày và bán các sản phẩm như rau củ hữu cơ, túi vải tái chế, đồ gia dụng từ vật liệu tự nhiên, mỹ phẩm không hóa chất. Sự kiện cũng bao gồm các workshop hướng dẫn làm sản phẩm handmade từ vật liệu tái chế, nấu ăn với nguyên liệu hữu cơ, và cách sống xanh trong đời sống hàng ngày. Đây là cơ hội tuyệt vời để cộng đồng tìm hiểu và tiếp cận với lối sống bền vững, đồng thời hỗ trợ các doanh nghiệp xã hội và nhà sản xuất địa phương.',
                'location' => 'Công viên 23/9, Quận 1, TP.HCM',
                'image' => 'events/eco-market-q1.jpg'
            ],
            [
                'title' => 'Đạp xe vì môi trường xanh',
                'description' => 'Hoạt động đạp xe tuyên truyền bảo vệ môi trường và khuyến khích sử dụng phương tiện giao thông xanh.',
                'content' => 'Giao thông xanh là một trong những giải pháp hiệu quả để giảm thiểu ô nhiễm không khí và khí thải nhà kính. Hoạt động đạp xe này nhằm tuyên truyền lợi ích của việc sử dụng xe đạp như phương tiện di chuyển hàng ngày. Lộ trình đi qua các điểm nổi tiếng của thành phố, dừng chân tại các khu vực có vấn đề môi trường để tuyên truyền và phát tờ rơi. Người tham gia sẽ được trang bị áo đồng phục, mũ bảo hiểm và các vật dụng tuyên truyền. Sự kiện cũng bao gồm các hoạt động như thi đua đạp xe, trò chơi giáo dục về môi trường và trao giải cho các cá nhân có đóng góp tích cực cho hoạt động bảo vệ môi trường.',
                'location' => 'Bờ sông Sài Gòn, Quận 1, TP.HCM',
                'image' => 'events/cycling-environment.jpg'
            ]
        ];

        // Chọn ngẫu nhiên một sự kiện từ danh sách
        $eventData = $this->faker->randomElement($environmentalEvents);

        // Tạo thời gian đa dạng: quá khứ, hiện tại, tương lai
        $timeRange = $this->faker->randomElement([
            'past',     // Sự kiện đã kết thúc
            'current',  // Sự kiện đang diễn ra
            'future'    // Sự kiện sắp diễn ra
        ]);

        switch ($timeRange) {
            case 'past':
                $start = $this->faker->dateTimeBetween('-2 months', '-1 week');
                $end = (clone $start)->modify('+'.rand(2,6).' hours');
                break;
            case 'current':
                $start = $this->faker->dateTimeBetween('-1 week', 'now');
                $end = $this->faker->dateTimeBetween('now', '+1 week');
                break;
            case 'future':
            default:
                $start = $this->faker->dateTimeBetween('+1 day', '+2 months');
                $end = (clone $start)->modify('+'.rand(2,8).' hours');
                break;
        }

        // Tất cả sự kiện đều approved
        $status = 'approved';
        $isActive = true;

        return [
            'title' => $eventData['title'],
            'slug' => Str::slug($eventData['title']) . '-' . Str::random(8),
            'description' => $eventData['description'],
            'content' => $eventData['content'],
            'location' => $eventData['location'],
            'start_date' => $start,
            'end_date' => $this->faker->boolean(90) ? $end : null, // 10% không có end_date
            'featured_image' => $this->faker->boolean(85) ? $eventData['image'] : null,
            'is_active' => $isActive,
            'status' => $status,
            'max_participants' => $this->faker->randomElement([
                null, // Không giới hạn
                $this->faker->numberBetween(20, 50),   // Sự kiện nhỏ
                $this->faker->numberBetween(50, 150),  // Sự kiện trung bình
                $this->faker->numberBetween(150, 300), // Sự kiện lớn
            ]),
            'created_by' => \App\Models\Account::inRandomOrder()->value('id') ?? 1,
        ];
    }

    /**
     * Tạo sự kiện đang chờ duyệt
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'is_active' => false,
            'start_date' => $this->faker->dateTimeBetween('+1 day', '+1 month'),
        ]);
    }

    /**
     * Tạo sự kiện đã được duyệt
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'approved',
            'is_active' => true,
        ]);
    }

    /**
     * Tạo sự kiện bị từ chối
     */
    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'rejected',
            'is_active' => false,
        ]);
    }

    /**
     * Tạo sự kiện đã kết thúc
     */
    public function finished(): static
    {
        return $this->state(function (array $attributes) {
            $start = $this->faker->dateTimeBetween('-2 months', '-1 week');
            $end = (clone $start)->modify('+'.rand(2,6).' hours');

            return [
                'start_date' => $start,
                'end_date' => $end,
                'status' => 'approved',
                'is_active' => true,
            ];
        });
    }

    /**
     * Tạo sự kiện đang diễn ra
     */
    public function ongoing(): static
    {
        return $this->state(function (array $attributes) {
            $start = $this->faker->dateTimeBetween('-3 days', 'now');
            $end = $this->faker->dateTimeBetween('now', '+3 days');

            return [
                'start_date' => $start,
                'end_date' => $end,
                'status' => 'approved',
                'is_active' => true,
            ];
        });
    }

    /**
     * Tạo sự kiện sắp diễn ra
     */
    public function upcoming(): static
    {
        return $this->state(function (array $attributes) {
            $start = $this->faker->dateTimeBetween('+1 day', '+2 months');
            $end = (clone $start)->modify('+'.rand(2,8).' hours');

            return [
                'start_date' => $start,
                'end_date' => $end,
                'status' => 'approved',
                'is_active' => true,
            ];
        });
    }
} 