<?php
use App\Models\Translation;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;

if (!function_exists('trans_db')) {
    function trans_db($key, $locale = null)
    {
        $locale = $locale ?? App::getLocale();

        // Simple cache key
        $cacheKey = "trans_{$locale}_{$key}";

        return Cache::remember($cacheKey, 3600, function () use ($key, $locale) {
            $translation = Translation::where('key', $key)
                ->where('locale', $locale)
                ->value('value');

            if ($translation) {
                return $translation;
            }

            // Fallback to default language
            $defaultLocale = \App\Models\Language::getDefaultLocale();
            if ($locale !== $defaultLocale) {
                $fallbackTranslation = Translation::where('key', $key)
                    ->where('locale', $defaultLocale)
                    ->value('value');

                if ($fallbackTranslation) {
                    return $fallbackTranslation;
                }
            }



            return $key;
        });
    }
}





if (!function_exists('localized_route')) {
    function localized_route($name, $parameters = [], $absolute = true): string
    {
        // Check if route exists and has locale parameter
        try {
            $route = \Illuminate\Support\Facades\Route::getRoutes()->getByName($name);
            if ($route && str_contains($route->uri(), '{locale}')) {
                $parameters = array_merge(['locale' => app()->getLocale()], $parameters);
            }
            return route($name, $parameters, $absolute);
        } catch (\Exception $e) {
            // Fallback to regular route if there's an issue
            return route($name, $parameters, $absolute);
        }
    }
}

if (!function_exists('get_active_languages')) {
    function get_active_languages()
    {
        return \App\Models\Language::getActive();
    }
}

if (!function_exists('get_default_language')) {
    function get_default_language(): ?\App\Models\Language
    {
        return \App\Models\Language::getDefault();
    }
}

if (!function_exists('is_locale_available')) {
    function is_locale_available(string $locale): bool
    {
        return \App\Models\Language::isLocaleAvailable($locale);
    }
}

if (!function_exists('get_current_language')) {
    /**
     * Get current language model (cached)
     */
    function get_current_language(): ?\App\Models\Language
    {
        $currentLocale = app()->getLocale();
        return Cache::remember("current_language_{$currentLocale}", 3600, function () use ($currentLocale) {
            return \App\Models\Language::where('locale', $currentLocale)->first();
        });
    }
}

if (!function_exists('translate_or_fallback')) {
    /**
     * Get translation with parameter replacement and fallback
     */
    function translate_or_fallback(string $key, array $replace = [], string $locale = null): string
    {
        $locale = $locale ?? app()->getLocale();

        // Try database translation first
        $dbTranslation = trans_db($key, $locale);
        if ($dbTranslation !== $key) {
            return empty($replace) ? $dbTranslation : str_replace(array_keys($replace), array_values($replace), $dbTranslation);
        }

        // Fallback to Laravel's translation system
        $laravelTranslation = trans($key, $replace, $locale);
        return $laravelTranslation !== $key ? $laravelTranslation : $key;
    }
}

if (!function_exists('get_localized_url')) {
    function get_localized_url(string $locale, string $url = null): string
    {
        $currentRoute = request()->route();

        if (!$currentRoute) {
            return "/{$locale}";
        }

        $routeName = $currentRoute->getName();
        $parameters = $currentRoute->parameters();

        // Replace locale parameter
        $parameters['locale'] = $locale;

        try {
            // Try to generate route with new locale
            return route($routeName, $parameters);
        } catch (\Exception $e) {
            // Fallback: construct URL manually
            $currentUrl = $url ?? request()->url();
            $currentLocale = app()->getLocale();

            // If current URL has locale prefix, replace it
            if (preg_match('/\/(' . $currentLocale . ')\//', $currentUrl)) {
                return preg_replace('/\/(' . $currentLocale . ')\//', "/{$locale}/", $currentUrl);
            } elseif (preg_match('/\/(' . $currentLocale . ')$/', $currentUrl)) {
                return preg_replace('/\/(' . $currentLocale . ')$/', "/{$locale}", $currentUrl);
            } else {
                // If no locale in URL, add it
                $path = parse_url($currentUrl, PHP_URL_PATH);
                $baseUrl = str_replace($path, '', $currentUrl);
                return $baseUrl . "/{$locale}" . ($path === '/' ? '' : $path);
            }
        }
    }
}

if (!function_exists('get_flag_emoji')) {
    function get_flag_emoji(string $locale): string
    {
        $flags = [
            'en' => '🇺🇸',
            'vi' => '🇻🇳',
            'fr' => '🇫🇷',
            'de' => '🇩🇪',
            'es' => '🇪🇸',
            'it' => '🇮🇹',
            'ja' => '🇯🇵',
            'ko' => '🇰🇷',
            'zh' => '🇨🇳',
            'ru' => '🇷🇺',
            'pt' => '🇵🇹',
            'ar' => '🇸🇦',
            'hi' => '🇮🇳',
            'th' => '🇹🇭',
        ];

        return $flags[$locale] ?? '🌐';
    }
}

if (!function_exists('format_locale_name')) {
    function format_locale_name(string $locale): string
    {
        $names = [
            'en' => 'English',
            'vi' => 'Tiếng Việt',
            'fr' => 'Français',
            'de' => 'Deutsch',
            'es' => 'Español',
            'it' => 'Italiano',
            'ja' => '日本語',
            'ko' => '한국어',
            'zh' => '中文',
            'ru' => 'Русский',
            'pt' => 'Português',
            'ar' => 'العربية',
            'hi' => 'हिन्दी',
            'th' => 'ไทย',
        ];

        return $names[$locale] ?? strtoupper($locale);
    }
}

if (!function_exists('get_missing_translations')) {
    function get_missing_translations(string $locale = null): array
    {
        $locale = $locale ?? app()->getLocale();
        return \App\Models\Translation::getMissingForLocale($locale);
    }
}

if (!function_exists('get_translation_progress')) {
    /**
     * Get translation progress for a locale (cached)
     */
    function get_translation_progress(string $locale = null): array
    {
        $locale = $locale ?? app()->getLocale();

        return Cache::remember("translation_progress_{$locale}", 1800, function () use ($locale) {
            $totalKeys = \App\Models\Translation::distinct()->count('key');
            $translatedKeys = \App\Models\Translation::where('locale', $locale)->count();
            $percentage = $totalKeys > 0 ? round(($translatedKeys / $totalKeys) * 100, 2) : 0;

            return [
                'total' => $totalKeys,
                'translated' => $translatedKeys,
                'missing' => $totalKeys - $translatedKeys,
                'percentage' => $percentage
            ];
        });
    }
}

if (!function_exists('formatDateTime')) {
    function formatDateTime($date, $format = 'Y-m-d H:i:s') {
        if (!$date) return null;
        if ($date instanceof \Carbon\Carbon || $date instanceof \DateTime) {
            return $date->format($format);
        }
        // Nếu là string, cố gắng parse
        try {
            return \Carbon\Carbon::parse($date)->format($format);
        } catch (\Exception $e) {
            return $date;
        }
    }
}



