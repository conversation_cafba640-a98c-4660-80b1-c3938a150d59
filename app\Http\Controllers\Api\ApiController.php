<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;

/**
 * @OA\Info(
 *     title="Dọn Rác API Documentation",
 *     version="1.0.0",
 *     description="API Documentation cho ứng dụng Dọn <PERSON> - <PERSON>ệ thống quản lý môi trường và thu gom rác",
 *     @OA\Contact(
 *         email="<EMAIL>"
 *     )
 * )
 * 
 * @OA\Server(
 *     url="http://127.0.0.1:8000",
 *     description="Local Development Server"
 * )
 * 
 * @OA\SecurityScheme(
 *     securityScheme="sanctum",
 *     type="http",
 *     scheme="bearer",
 *     bearerFormat="JWT",
 *     description="Enter token in format (Bearer <token>)"
 * )
 * 
 * 
 * @OA\Tag(
 *     name="Authentication",
 *     description="Authentication endpoints"
 * )
 * 
 * @OA\Tag(
 *     name="Leaderboard", 
 *     description="Leaderboard and ranking endpoints"
 * )
 * 
 * @OA\Tag(
 *     name="Forum",
 *     description="Forum and discussion endpoints"
 * )
 * 
 * @OA\Tag(
 *     name="Map",
 *     description="Map and location endpoints"
 * )
 * 
 * @OA\Tag(
 *     name="Reports",
 *     description="Report submission endpoints"
 * )
 * 
 * @OA\Tag(
 *     name="Gifts",
 *     description="Gift redemption endpoints"
 * )
 * 
 * @OA\Tag(
 *     name="Profile",
 *     description="User profile management endpoints"
 * )
 * 
 * @OA\Tag(
 *     name="Events",
 *     description="Event management endpoints"
 * )
 */
class ApiController extends Controller
{
    //
} 