<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Account;
use App\Models\PointHistory;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;

class ApiAuthController extends Controller
{
    /**
     * @OA\Post(
     *     path="/api/app/login",
     *     tags={"Authentication"},
     *     summary="Đăng nhập app",
     *     description="Đăng nhập cho mobile app với email hoặc username",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 required={"email", "password"},
     *                 @OA\Property(property="email", type="string", example="<EMAIL>", description="Email hoặc username"),
     *                 @OA\Property(property="password", type="string", example="123456", description="Mật khẩu"),
     *                 @OA\Property(property="remember", type="boolean", example=false, description="Ghi nhớ đăng nhập")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Đăng nhập thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Đăng nhập thành công"),
     *             @OA\Property(property="user", type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="username", type="string", example="john_doe"),
     *                 @OA\Property(property="email", type="string", example="<EMAIL>"),
     *                 @OA\Property(property="full_name", type="string", example="John Doe"),
     *                 @OA\Property(property="point", type="integer", example=105)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Thông tin đăng nhập không đúng",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Thông tin đăng nhập không đúng")
     *         )
     *     )
     * )
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|string',
            'password' => 'required|string',
        ]);

        // Cho phép đăng nhập bằng email hoặc username
        $login_type = filter_var($request->email, FILTER_VALIDATE_EMAIL) ? 'email' : 'username';
        
        // Tìm account để verify
        $account = Account::where($login_type, $request->email)->first();
        
        if (!$account || !Hash::check($request->password, $account->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Email/tên đăng nhập hoặc mật khẩu không đúng!'
            ], 401);
        }

        // Kiểm tra account có active không
        if (!$account->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Tài khoản đã bị vô hiệu hóa!'
            ], 403);
        }

        // Xóa các token cũ (tuỳ chọn - để multi-device thì bỏ dòng này)
        $account->tokens()->delete();

        // Tạo token mới
        $token = $account->createToken('mobile-app-token')->plainTextToken;

        // Cộng điểm đăng nhập
        $lastPointHistory = PointHistory::where('account_id', $account->id)
            ->orderByDesc('created_at')
            ->first();
        $now = Carbon::now();
        $shouldAddPoint = false;
        if (!$lastPointHistory) {
            $shouldAddPoint = true;
        } else {
            $lastLogin = Carbon::parse($lastPointHistory->created_at);
            if ($now->diffInMinutes($lastLogin) >= 10) {
                $shouldAddPoint = true;
            }
        }
        if ($shouldAddPoint) {
            $account->point += 5;
            $account->save();
            PointHistory::create([
                'account_id' => $account->id,
                'change' => 5,
                'description' => 'Cộng điểm khi đăng nhập',
            ]);
        }

        return response()->json([
            'success' => true, 
            'message' => 'Đăng nhập thành công!',
            'token' => $token,
            'user' => [
                'id' => $account->id,
                'username' => $account->username,
                'email' => $account->email,
                'full_name' => $account->full_name,
                'avatar' => $account->avatar,
                'point' => $account->point,
                'is_active' => $account->is_active,
            ]
        ]);
    }

    /**
     * Web login method for session-based authentication
     */
    public function webLogin(Request $request)
    {
        $request->validate([
            'email' => 'required|string',
            'password' => 'required|string',
        ]);

        // Cho phép đăng nhập bằng email hoặc username
        $login_type = filter_var($request->email, FILTER_VALIDATE_EMAIL) ? 'email' : 'username';

        $credentials = [
            $login_type => $request->email,
            'password' => $request->password,
        ];

        if (Auth::guard('account')->attempt($credentials, $request->filled('remember'))) {
            $request->session()->regenerate();

            $account = Auth::guard('account')->user();

            // Cộng điểm đăng nhập
            $lastPointHistory = PointHistory::where('account_id', $account->id)
                ->orderByDesc('created_at')
                ->first();
            $now = Carbon::now();
            $shouldAddPoint = false;
            if (!$lastPointHistory) {
                $shouldAddPoint = true;
            } else {
                $lastLogin = Carbon::parse($lastPointHistory->created_at);
                if ($now->diffInMinutes($lastLogin) >= 10) {
                    $shouldAddPoint = true;
                }
            }
            if ($shouldAddPoint) {
                $account->point += 5;
                $account->save();
                PointHistory::create([
                    'account_id' => $account->id,
                    'change' => 5,
                    'description' => 'Cộng điểm khi đăng nhập',
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Đăng nhập thành công!',
                'user' => [
                    'id' => $account->id,
                    'username' => $account->username,
                    'email' => $account->email,
                    'full_name' => $account->full_name,
                    'avatar' => $account->avatar,
                    'point' => $account->point,
                    'is_active' => $account->is_active,
                ]
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Email/tên đăng nhập hoặc mật khẩu không đúng!'
        ], 401);
    }

    /**
     * @OA\Post(
     *     path="/api/app/register",
     *     tags={"Authentication"},
     *     summary="Đăng ký app nhanh",
     *     description="Đăng ký tài khoản nhanh cho mobile app",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"full_name", "email", "password", "confirm_password"},
     *             @OA\Property(property="full_name", type="string", example="Nguyễn Văn A"),
     *             @OA\Property(property="email", type="string", example="<EMAIL>"),
     *             @OA\Property(property="password", type="string", example="123456"),
     *             @OA\Property(property="confirm_password", type="string", example="123456")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Đăng ký thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Đăng ký thành công")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Dữ liệu không hợp lệ",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Dữ liệu không hợp lệ"),
     *             @OA\Property(property="errors", type="object")
     *         )
     *     )
     * )
     */
    public function appRegister(Request $request)
    {
        $request->validate([
            'full_name' => 'required|string|max:255',
            'email' => 'required|email|unique:accounts,email',
            'password' => 'required|string|min:6',
            'confirm_password' => 'required|string|same:password',
        ]);

        $email = $request->email;
        $username = explode('@', $email)[0];
        
        // Đảm bảo username unique
        $originalUsername = $username;
        $counter = 1;
        while (Account::where('username', $username)->exists()) {
            $username = $originalUsername . $counter;
            $counter++;
        }

        $account = Account::create([
            'full_name' => $request->full_name,
            'username' => $username,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'is_active' => true,
        ]);

        // Tạo token cho user mới
        $token = $account->createToken('mobile-app-token')->plainTextToken;

        return response()->json([
            'success' => true, 
            'message' => 'Đăng ký thành công!', 
            'token' => $token,
            'user' => [
                'id' => $account->id,
                'username' => $account->username,
                'email' => $account->email,
                'full_name' => $account->full_name,
                'avatar' => $account->avatar,
                'point' => $account->point,
                'is_active' => $account->is_active,
            ]
        ]);
    }

    /**
     * @OA\Post(
     *     path="/api/app/forgot-password",
     *     tags={"Authentication"},
     *     summary="Quên mật khẩu",
     *     description="Gửi link reset mật khẩu qua email",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"email"},
     *             @OA\Property(property="email", type="string", example="<EMAIL>")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Gửi email thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Link reset mật khẩu đã được gửi")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Email không tồn tại",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Email không tồn tại")
     *         )
     *     )
     * )
     */
    public function appForgotPassword(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
        ]);

        $account = Account::where('email', $request->email)->first();
        if (!$account) {
            return response()->json([
                'success' => false, 
                'message' => 'Email không tồn tại!'
            ], 404);
        }

        $token = Str::random(60);
        DB::table('password_resets')->updateOrInsert(
            ['email' => $request->email],
            [
                'token' => $token,
                'created_at' => Carbon::now()
            ]
        );

        try {
            Mail::to($request->email)->send(new \App\Mail\ResetPasswordLink($token, $request->email));
            return response()->json([
                'success' => true, 
                'message' => 'Đã gửi email khôi phục mật khẩu!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false, 
                'message' => 'Không thể gửi email. Vui lòng thử lại!'
            ], 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/logout",
     *     tags={"Authentication"},
     *     summary="Đăng xuất",
     *     description="Đăng xuất khỏi hệ thống",
     *     security={{"sanctum": {}}},
     *     @OA\Response(
     *         response=200,
     *         description="Đăng xuất thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Đăng xuất thành công")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Chưa đăng nhập",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Chưa đăng nhập")
     *         )
     *     )
     * )
     */
    public function logout(Request $request)
    {
        // Xóa token hiện tại
        $request->user()->currentAccessToken()->delete();
        
        return response()->json([
            'success' => true, 
            'message' => 'Đã đăng xuất!'
        ]);
    }

    public function refreshToken(Request $request)
    {
        $user = $request->user();
        
        // Xóa token hiện tại
        $request->user()->currentAccessToken()->delete();
        
        // Tạo token mới
        $token = $user->createToken('mobile-app-token')->plainTextToken;
        
        return response()->json([
            'success' => true,
            'message' => 'Token đã được làm mới!',
            'token' => $token
        ]);
    }
}
