@extends('layouts.app')
@section('title', trans_db('general.danh_sach_su_kien'))
@section('header', trans_db('general.danh_sach_su_kien'))
@section('content')
<div class="container mx-auto py-8">
    <h2 class="text-3xl font-bold text-center mb-6 bg-gradient-to-r from-[#10B981] to-[#0EA5E9] bg-clip-text text-transparent">{{ trans_db('general.danh_sach_su_kien') }}</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @forelse($events as $event)
            @php
                $now = \Carbon\Carbon::now();
                $start = \Carbon\Carbon::parse($event->start_date);
                $end = $event->end_date ? \Carbon\Carbon::parse($event->end_date) : null;
                if ($now->lt($start)) {
                    $status = [trans_db('general.sap_dien_ra'), 'bg-yellow-400', 'text-yellow-900'];
                } elseif ($end && $now->gt($end)) {
                    $status = [trans_db('general.ket_thuc'), 'bg-gray-400', 'text-gray-800'];
                } else {
                    $status = [trans_db('general.dang_dien_ra'), 'bg-green-500', 'text-white'];
                }
            @endphp
            <a href="{{ localized_route('event.show', ['slug' => $event->slug]) }}" class="relative bg-white rounded-2xl shadow-2xl p-4 flex flex-col hover:shadow-lg transition group border border-[#10B981]/20 hover:border-[#0EA5E9]">
                <span class="absolute top-3 right-3 px-3 py-1 rounded-full text-xs font-bold {{ $status[1] }} {{ $status[2] }}">
                    {{ $status[0] }}
                </span>
                @if($event->featured_image)
                    <img src="{{ asset('storage/'.$event->featured_image) }}" alt="{{ $event->title }}" class="rounded mb-3 w-full h-40 object-cover border border-[#10B981]/30">
                @else
                    <img src="https://placehold.co/600x400?text=No+Image" alt="No Image" class="rounded mb-3 w-full h-40 object-cover border border-[#10B981]/30">
                @endif
                <h3 class="text-lg font-bold bg-gradient-to-r from-[#10B981] to-[#0EA5E9] bg-clip-text text-transparent mb-1 group-hover:underline">{{ $event->title }}</h3>
                <div class="text-sm text-gray-500 mb-1">
                    <span class="font-medium text-[#10B981]">{{ trans_db('general.thoi_gian') }}:</span>
                    {{ \Carbon\Carbon::parse($event->start_date)->format('d/m/Y H:i') }}
                    @if($event->end_date)
                        - {{ \Carbon\Carbon::parse($event->end_date)->format('d/m/Y H:i') }}
                    @endif
                </div>
                <div class="text-sm text-gray-500 mb-2">
                    <span class="font-medium text-[#0EA5E9]">{{ trans_db('general.dia_diem') }}:</span> {{ $event->location }}
                </div>
                @if($event->max_participants)
                <div class="text-xs text-gray-400 mb-2">
                    <span class="font-medium">{{ trans_db('general.so_nguoi_toi_da') }}:</span> {{ $event->max_participants }}
                </div>
                @endif
                <div class="text-gray-700 mb-3">{{ Str::limit($event->description, 100) }}</div>
            </a>
        @empty
            <div class="col-span-3 text-center text-gray-500">{{ trans_db('common.chua_co_su_kien_nao') }}</div>
        @endforelse
    </div>
</div>
@endsection
