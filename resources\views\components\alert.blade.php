@props([
    'type',
    'dismissible' => false
])

@php
    $classes = match($type) {
        'success-700',
        'error-700',
        'warning-700',
        default => 'bg-blue-100 border-blue-400 text-blue-700',
    };
@endphp

<div {{ $attributes->merge(['class-4"]) }} role="alert-5 w-5 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd')
            <svg class="h-5 w-5 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd')
            <svg class="h-5 w-5 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd"/>
            </svg>
        @endif
        
        <div>
            {{ $slot }}
        </div>
    </div>
    
    @if($dismissible)
        <button type="button-4" onclick="this.parentElement.style.display='none="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd"/>
            </svg>
        </button>
    @endif
</div>