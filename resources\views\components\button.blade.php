@props([
    'type',
    'color',
    'size',
    'href' => false
])

@php
    $baseClasses = 'inline-flex items-center justify-center font-semibold tracking-widest focus:outline-none focus:ring transition ease-in-out duration-150';
    
    $colorClasses = match($color) {
        'primary-300',
        'secondary-200',
        'success-300',
        'danger-300',
        'warning-200',
        'info-200',
        'light-100',
        'dark-300',
        'link-100',
        default => 'bg-indigo-600 border border-transparent text-white hover:bg-indigo-700 active:bg-indigo-800 focus:border-indigo-800 focus:ring-indigo-300',
    };
    
    $sizeClasses = match($size) {
        'xs',
        'sm',
        'md',
        'lg',
        'xl',
        default => 'px-4 py-2 text-sm rounded-md' : '';
    
    $classes = "$baseClasses $colorClasses $sizeClasses $disabledClasses";
@endphp

@if($href && !$disabled)
    <a href="{{ $href }}" {{ $attributes->merge(['class' => $classes]) }}>
        {{ $slot }}
    </a>
@else
    <button type="{{ $type }}" {{ $attributes->merge(['class' => $disabled]) }}>
        {{ $slot }}
    </button>
@endif