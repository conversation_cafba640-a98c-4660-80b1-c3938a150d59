<svg width="600" height="400" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#0EA5E9;stop-opacity:0.1" />
    </linearGradient>
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0EA5E9;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="600" height="400" fill="url(#bgGradient)" />
  
  <!-- Border -->
  <rect x="2" y="2" width="596" height="396" fill="none" stroke="#10B981" stroke-width="2" stroke-opacity="0.3" rx="8" />
  
  <!-- Main icon - Calendar with leaf -->
  <g transform="translate(300, 200)">
    <!-- Calendar base -->
    <rect x="-60" y="-40" width="120" height="80" rx="8" fill="url(#iconGradient)" opacity="0.8" />
    
    <!-- Calendar header -->
    <rect x="-60" y="-40" width="120" height="20" rx="8" fill="url(#iconGradient)" />
    
    <!-- Calendar rings -->
    <circle cx="-35" cy="-50" r="3" fill="url(#iconGradient)" />
    <circle cx="35" cy="-50" r="3" fill="url(#iconGradient)" />
    
    <!-- Calendar grid -->
    <g stroke="white" stroke-width="1" opacity="0.6">
      <line x1="-40" y1="-15" x2="-40" y2="25" />
      <line x1="-20" y1="-15" x2="-20" y2="25" />
      <line x1="0" y1="-15" x2="0" y2="25" />
      <line x1="20" y1="-15" x2="20" y2="25" />
      <line x1="40" y1="-15" x2="40" y2="25" />
      
      <line x1="-50" y1="-5" x2="50" y2="-5" />
      <line x1="-50" y1="5" x2="50" y2="5" />
      <line x1="-50" y1="15" x2="50" y2="15" />
    </g>
    
    <!-- Leaf decoration -->
    <g transform="translate(70, -60) scale(0.8)">
      <path d="M0,0 Q15,-10 30,0 Q15,20 0,0" fill="#10B981" opacity="0.7" />
      <path d="M0,0 Q15,5 30,0" stroke="#0EA5E9" stroke-width="1" fill="none" opacity="0.5" />
    </g>
    
    <!-- Recycling symbol -->
    <g transform="translate(-80, 60) scale(0.6)">
      <path d="M0,-15 L10,-5 L5,0 L-5,0 L-10,-5 Z" fill="#10B981" opacity="0.6" />
      <path d="M10,-5 L20,5 L15,10 L5,10 L0,5 Z" fill="#10B981" opacity="0.6" />
      <path d="M15,10 L5,20 L0,15 L0,5 L5,0 Z" fill="#10B981" opacity="0.6" />
    </g>
  </g>
  
  <!-- Text -->
  <text x="300" y="280" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#10B981">
    Sự kiện môi trường
  </text>
  
  <text x="300" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#6B7280">
    EcoSolves Community Event
  </text>
  
  <!-- Decorative elements -->
  <circle cx="100" cy="100" r="3" fill="#10B981" opacity="0.3" />
  <circle cx="500" cy="120" r="2" fill="#0EA5E9" opacity="0.4" />
  <circle cx="80" cy="320" r="2" fill="#10B981" opacity="0.3" />
  <circle cx="520" cy="300" r="3" fill="#0EA5E9" opacity="0.3" />
</svg>
