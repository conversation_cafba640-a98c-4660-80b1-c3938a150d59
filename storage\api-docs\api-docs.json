{"openapi": "3.0.0", "info": {"title": "Dọn Rác API Documentation", "description": "API Documentation cho ứng dụng <PERSON> - <PERSON><PERSON> thống quản lý môi trường và thu gom rác", "contact": {"email": "<EMAIL>"}, "version": "1.0.0"}, "servers": [{"url": "http://127.0.0.1:8000", "description": "Local Development Server"}], "paths": {"/api/app/login": {"post": {"tags": ["Authentication"], "summary": "Đ<PERSON><PERSON> nhập app", "description": "<PERSON><PERSON><PERSON> nhập cho mobile app với email hoặc username", "operationId": "a9bcfe4c98f2f0e9d9f1e1a7c62938c8", "requestBody": {"required": true, "content": {"application/x-www-form-urlencoded": {"schema": {"required": ["email", "password"], "properties": {"email": {"description": "Email hoặc username", "type": "string", "example": "<EMAIL>"}, "password": {"description": "<PERSON><PERSON><PERSON>", "type": "string", "example": "123456"}, "remember": {"description": "<PERSON><PERSON> nhớ đăng nhập", "type": "boolean", "example": false}}, "type": "object"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nh<PERSON>p thành công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nh<PERSON>p thành công"}, "user": {"properties": {"id": {"type": "integer", "example": 1}, "username": {"type": "string", "example": "john_doe"}, "email": {"type": "string", "example": "<EMAIL>"}, "full_name": {"type": "string", "example": "<PERSON>"}, "point": {"type": "integer", "example": 105}}, "type": "object"}}, "type": "object"}}}}, "401": {"description": "Thông tin đăng nhập không đúng", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Thông tin đăng nhập không đúng"}}, "type": "object"}}}}}}}, "/api/app/register": {"post": {"tags": ["Authentication"], "summary": "Đ<PERSON>ng ký <PERSON> nhanh", "description": "Đăng ký tài khoản nhanh cho mobile app", "operationId": "198e8421f5329095881e0cb4a5132224", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["full_name", "email", "password", "confirm_password"], "properties": {"full_name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "password": {"type": "string", "example": "123456"}, "confirm_password": {"type": "string", "example": "123456"}}, "type": "object"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> ký thành công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> ký thành công"}}, "type": "object"}}}}, "422": {"description": "<PERSON><PERSON> li<PERSON><PERSON> không hợp lệ", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "<PERSON><PERSON> li<PERSON><PERSON> không hợp lệ"}, "errors": {"type": "object"}}, "type": "object"}}}}}}}, "/api/app/forgot-password": {"post": {"tags": ["Authentication"], "summary": "<PERSON><PERSON><PERSON><PERSON> mật k<PERSON>u", "description": "Gửi link reset mật khẩu qua email", "operationId": "3c1e3295b0f69e23b352003d8c0db714", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["email"], "properties": {"email": {"type": "string", "example": "<EMAIL>"}}, "type": "object"}}}}, "responses": {"200": {"description": "Gửi email thành công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Link reset mật khẩu đã đ<PERSON><PERSON><PERSON> g<PERSON>i"}}, "type": "object"}}}}, "404": {"description": "<PERSON><PERSON> không tồn tại", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "<PERSON><PERSON> không tồn tại"}}, "type": "object"}}}}}}}, "/api/logout": {"post": {"tags": ["Authentication"], "summary": "<PERSON><PERSON><PERSON> xu<PERSON>", "description": "<PERSON><PERSON><PERSON> xuất khỏi hệ thống", "operationId": "16f50a80a3393cee8600d5a26f09075d", "responses": {"200": {"description": "<PERSON><PERSON><PERSON> xuất thành công", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "<PERSON><PERSON><PERSON> xuất thành công"}}, "type": "object"}}}}, "401": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/app/events": {"get": {"tags": ["Events"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch sự kiện", "description": "<PERSON><PERSON><PERSON> danh s<PERSON>ch sự kiện, c<PERSON> phân trang. <PERSON><PERSON><PERSON><PERSON> yêu cầu đăng nhập.", "operationId": "12fb3317a7dbbd361e0e11b9f8406daa", "parameters": [{"name": "page", "in": "query", "description": "Số trang", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "per_page", "in": "query", "description": "Số sự kiện mỗi trang (tối đa 50)", "required": false, "schema": {"type": "integer", "default": 15, "maximum": 50}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "<PERSON><PERSON> kiện môi trường"}, "slug": {"type": "string", "example": "su-kien-moi-truong-abc12"}, "description": {"type": "string", "example": "<PERSON><PERSON> t<PERSON> sự kiện"}, "content": {"type": "string", "example": "<PERSON><PERSON><PERSON> dung chi tiết sự kiện"}, "location": {"type": "string", "example": "<PERSON><PERSON>"}, "start_date": {"type": "string", "example": "2024-12-20 08:00:00"}, "end_date": {"type": "string", "example": "2024-12-20 17:00:00"}, "featured_image": {"type": "string", "example": "http://your-domain.com/storage/events/abc.jpg", "nullable": true}, "is_active": {"type": "boolean", "example": false}, "max_participants": {"type": "integer", "example": 100, "nullable": true}, "status": {"type": "string", "example": "pending"}, "created_by": {"type": "integer", "example": 2}, "joined": {"description": "1: user đã đăng ký, 0: chưa hoặc chưa đăng nhập", "type": "integer", "example": 1}}, "type": "object"}}, "pagination": {"properties": {"current_page": {"type": "integer", "example": 1}, "per_page": {"type": "integer", "example": 15}, "total": {"type": "integer", "example": 20}, "last_page": {"type": "integer", "example": 2}, "has_more": {"type": "boolean", "example": false}}, "type": "object"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/app/events/create": {"post": {"tags": ["Events"], "summary": "<PERSON><PERSON><PERSON> mới sự kiện", "description": "<PERSON><PERSON><PERSON> mới một sự ki<PERSON>, yê<PERSON> c<PERSON>u đăng <PERSON> (sanctum)", "operationId": "54645d9726e3e7c6405e95e4bbe5ed17", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"required": ["title", "description", "content", "location", "start_date", "end_date"], "properties": {"title": {"type": "string", "example": "<PERSON><PERSON> kiện môi trường"}, "description": {"type": "string", "example": "<PERSON><PERSON> t<PERSON> sự kiện"}, "content": {"type": "string", "example": "<PERSON><PERSON><PERSON> dung chi tiết sự kiện"}, "location": {"type": "string", "example": "<PERSON><PERSON>"}, "start_date": {"type": "string", "format": "date-time", "example": "2024-12-20 08:00:00"}, "end_date": {"type": "string", "format": "date-time", "example": "2024-12-20 17:00:00"}, "featured_image": {"description": "Ảnh sự kiện (jpg, png, jpeg, gif, svg, max 4MB)", "type": "string", "format": "binary", "nullable": true}, "max_participants": {"type": "integer", "example": 100, "nullable": true}}, "type": "object"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> sự kiện thành công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Tạo sự kiện thành công!"}, "data": {"properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "<PERSON><PERSON> kiện môi trường"}, "slug": {"type": "string", "example": "su-kien-moi-truong-abc12"}, "description": {"type": "string", "example": "<PERSON><PERSON> t<PERSON> sự kiện"}, "content": {"type": "string", "example": "<PERSON><PERSON><PERSON> dung chi tiết sự kiện"}, "location": {"type": "string", "example": "<PERSON><PERSON>"}, "start_date": {"type": "string", "example": "2024-12-20 08:00:00"}, "end_date": {"type": "string", "example": "2024-12-20 17:00:00"}, "featured_image": {"type": "string", "example": "/storage/events/abc.jpg", "nullable": true}, "is_active": {"type": "boolean", "example": true}, "max_participants": {"type": "integer", "example": 100, "nullable": true}, "status": {"type": "string", "example": "active"}, "created_by": {"type": "integer", "example": 2}, "creator": {"properties": {"id": {"type": "integer", "example": 2}, "username": {"type": "string", "example": "user123"}, "full_name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}}, "type": "object"}}, "type": "object"}}, "type": "object"}}}}, "401": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Bạn cần đăng nhập!"}}, "type": "object"}}}}, "422": {"description": "Lỗi validate", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "The given data was invalid."}, "errors": {"type": "object"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/app/events/by-me": {"get": {"tags": ["Events"], "summary": "<PERSON><PERSON><PERSON> danh sách sự kiện do tôi tạo", "description": "<PERSON><PERSON><PERSON> danh sách sự kiện do user hiện tại tạo, c<PERSON> phân trang. <PERSON><PERSON><PERSON> cầu đăng nh<PERSON>p (sanctum)", "operationId": "72ad34008ccc4a40aa8d8e676ad0b86b", "parameters": [{"name": "page", "in": "query", "description": "Số trang", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "per_page", "in": "query", "description": "Số sự kiện mỗi trang (tối đa 50)", "required": false, "schema": {"type": "integer", "default": 15, "maximum": 50}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "<PERSON><PERSON> kiện môi trường"}, "slug": {"type": "string", "example": "su-kien-moi-truong-abc12"}, "description": {"type": "string", "example": "<PERSON><PERSON> t<PERSON> sự kiện"}, "content": {"type": "string", "example": "<PERSON><PERSON><PERSON> dung chi tiết sự kiện"}, "location": {"type": "string", "example": "<PERSON><PERSON>"}, "start_date": {"type": "string", "example": "2024-12-20 08:00:00"}, "end_date": {"type": "string", "example": "2024-12-20 17:00:00"}, "featured_image": {"type": "string", "example": "http://your-domain.com/storage/events/abc.jpg", "nullable": true}, "is_active": {"type": "boolean", "example": false}, "max_participants": {"type": "integer", "example": 100, "nullable": true}, "status": {"type": "string", "example": "active"}, "created_by": {"type": "integer", "example": 2}, "creator": {"properties": {"id": {"type": "integer", "example": 2}, "username": {"type": "string", "example": "user123"}, "full_name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}}, "type": "object"}, "joined": {"description": "1: user đã đăng ký, 0: chưa hoặc chưa đăng nhập", "type": "integer", "example": 1}}, "type": "object"}}, "pagination": {"properties": {"current_page": {"type": "integer", "example": 1}, "per_page": {"type": "integer", "example": 15}, "total": {"type": "integer", "example": 20}, "last_page": {"type": "integer", "example": 2}, "has_more": {"type": "boolean", "example": false}}, "type": "object"}}, "type": "object"}}}}, "401": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Bạn cần đăng nhập!"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/app/events/joined": {"get": {"tags": ["Events"], "summary": "<PERSON><PERSON><PERSON> danh sách sự kiện đã tham gia", "description": "<PERSON><PERSON><PERSON> danh sách đăng ký sự kiện của user hi<PERSON><PERSON> tại, chỉ trả về thông tin đăng ký, tê<PERSON>, slug, đ<PERSON><PERSON> điể<PERSON>, thời gian bắt đầu và kết thúc sự kiện.", "operationId": "cc1ea00721a76458b29e055abd4af9d4", "parameters": [{"name": "page", "in": "query", "description": "Số trang", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "per_page", "in": "query", "description": "<PERSON><PERSON> bản ghi mỗi trang (tối đa 50)", "required": false, "schema": {"type": "integer", "default": 15, "maximum": 50}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "event_id": {"type": "integer", "example": 5}, "event_title": {"type": "string", "example": "<PERSON><PERSON> kiện môi trường"}, "event_slug": {"type": "string", "example": "su-kien-moi-truong-abc12"}, "event_location": {"type": "string", "example": "<PERSON><PERSON>"}, "event_start_date": {"type": "string", "example": "2024-12-20 08:00:00"}, "event_end_date": {"type": "string", "example": "2024-12-20 17:00:00"}, "registered_at": {"type": "string", "example": "2024-12-20 10:30:45"}}, "type": "object"}}, "pagination": {"properties": {"current_page": {"type": "integer", "example": 1}, "per_page": {"type": "integer", "example": 15}, "total": {"type": "integer", "example": 20}, "last_page": {"type": "integer", "example": 2}, "has_more": {"type": "boolean", "example": false}}, "type": "object"}}, "type": "object"}}}}, "401": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Bạn cần đăng nhập!"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/app/events/register-event": {"post": {"tags": ["Events"], "summary": "<PERSON><PERSON><PERSON> ký tham gia sự kiện", "description": "<PERSON><PERSON><PERSON> ký tham gia một sự kiện, yê<PERSON> c<PERSON>u đăng <PERSON> (sanctum)", "operationId": "f3e41db38221132c9b1b46903a9f4fa2", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["event_id"], "properties": {"event_id": {"description": "ID của s<PERSON> kiện", "type": "integer", "example": 1}}, "type": "object"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> ký thành công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON>ng ký tham gia sự kiện thành công!"}}, "type": "object"}}}}, "400": {"description": "<PERSON><PERSON> đăng ký hoặc sự kiện không hợp lệ", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Bạn đã đăng ký sự kiện này!"}}, "type": "object"}}}}, "401": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Bạn cần đăng nhập!"}}, "type": "object"}}}}, "404": {"description": "<PERSON><PERSON> kiện không tồn tại", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "<PERSON><PERSON> kiện không tồn tại"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/app/events/checkin": {"post": {"tags": ["Events"], "summary": "Check-in s<PERSON> kiện", "description": "User g<PERSON><PERSON> yêu cầu check-in sự kiện, status mặc định là pending. <PERSON><PERSON><PERSON> cầu đăng nhập (sanctum)", "operationId": "31d07cf6e618d32945e4149b9c3ceb48", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["event_id", "lat", "long"], "properties": {"event_id": {"type": "integer", "example": 1}, "lat": {"type": "number", "format": "float", "example": 21.0285}, "long": {"type": "number", "format": "float", "example": 105.8542}}, "type": "object"}}}}, "responses": {"201": {"description": "Check-in thành công (đang chờ duyệt)", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"properties": {"id": {"type": "integer", "example": 1}, "user_id": {"type": "integer", "example": 2}, "event_id": {"type": "integer", "example": 1}, "lat": {"type": "number", "example": 21.0285}, "long": {"type": "number", "example": 105.8542}, "status": {"type": "string", "example": "pending"}, "created_at": {"type": "string", "example": "2024-07-01 10:00:00"}}, "type": "object"}}, "type": "object"}}}}, "401": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Bạn cần đăng nhập!"}}, "type": "object"}}}}, "422": {"description": "Lỗi validate", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "The given data was invalid."}, "errors": {"type": "object"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/app/forum": {"get": {"tags": ["Forum"], "summary": "<PERSON><PERSON><PERSON> danh s<PERSON>ch <PERSON>", "description": "<PERSON><PERSON><PERSON> danh sách threads với phân trang và tìm kiếm", "operationId": "10345c39632a93f4f023cfdbc4edc08a", "parameters": [{"name": "page", "in": "query", "description": "Số trang", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "per_page", "in": "query", "description": "<PERSON><PERSON> bài viết mỗi trang (tối đa 50)", "required": false, "schema": {"type": "integer", "default": 15, "maximum": 50}}, {"name": "search", "in": "query", "description": "<PERSON>ừ khóa tìm kiếm", "required": false, "schema": {"type": "string"}}, {"name": "sort_by", "in": "query", "description": "<PERSON><PERSON><PERSON> xếp theo", "required": false, "schema": {"type": "string", "default": "created_at", "enum": ["created_at", "updated_at", "views", "title"]}}, {"name": "sort_order", "in": "query", "description": "<PERSON><PERSON><PERSON> tự sắp xếp", "required": false, "schema": {"type": "string", "default": "desc", "enum": ["asc", "desc"]}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"properties": {"threads": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON> lu<PERSON> về môi trường"}, "slug": {"type": "string", "example": "thao-luan-ve-moi-truong"}, "content": {"type": "string", "example": "<PERSON><PERSON>i dung bài viết..."}, "views": {"type": "integer", "example": 150}, "replies_count": {"type": "integer", "example": 5}, "created_at": {"type": "string", "example": "2024-12-20T10:30:45.000000Z"}, "account": {"properties": {"id": {"type": "integer", "example": 1}, "username": {"type": "string", "example": "john_doe"}, "full_name": {"type": "string", "example": "<PERSON>"}}, "type": "object"}}, "type": "object"}}, "pagination": {"properties": {"total": {"type": "integer", "example": 100}, "per_page": {"type": "integer", "example": 15}, "current_page": {"type": "integer", "example": 1}, "last_page": {"type": "integer", "example": 7}}, "type": "object"}}, "type": "object"}}, "type": "object"}}}}}}}, "/api/app/forum/{slug}": {"get": {"tags": ["Forum"], "summary": "<PERSON>em chi tiết thread", "description": "<PERSON><PERSON> chi t<PERSON><PERSON><PERSON> thread theo slug v<PERSON><PERSON> danh s<PERSON>ch replies gốc (parent_id = 0 hoặc null)", "operationId": "6f13a4f1ea4ed0566ce86d56ebb6b704", "parameters": [{"name": "slug", "in": "path", "description": "Slug của thread", "required": true, "schema": {"type": "string", "example": "thao-luan-ve-moi-truong-abc123"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> chi tiết thread thành công"}, "data": {"properties": {"thread": {"properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON> lu<PERSON> về môi trường"}, "slug": {"type": "string", "example": "thao-luan-ve-moi-truong-abc123"}, "content": {"type": "string", "example": "<PERSON><PERSON><PERSON> dung bài viết chi tiết về môi trường..."}, "views": {"type": "integer", "example": 151}, "created_at": {"type": "string", "example": "2024-12-20T10:30:45.000000Z"}, "updated_at": {"type": "string", "example": "2024-12-20T10:30:45.000000Z"}, "children_count": {"type": "integer", "example": 0}, "account": {"properties": {"id": {"type": "integer", "example": 1}, "username": {"type": "string", "example": "john_doe"}, "full_name": {"type": "string", "example": "<PERSON>"}, "avatar": {"type": "string", "example": "http://example.com/avatar.jpg", "nullable": true}}, "type": "object"}}, "type": "object"}, "replies": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "content": {"type": "string", "example": "<PERSON><PERSON><PERSON> luận gốc về chủ đề này"}, "thread_id": {"type": "integer", "example": 1}, "parent_id": {"type": "integer", "example": null, "nullable": true}, "created_at": {"type": "string", "example": "2024-12-20T11:00:00.000000Z"}, "updated_at": {"type": "string", "example": "2024-12-20T11:00:00.000000Z"}, "children_count": {"type": "integer", "example": 0}, "account": {"properties": {"id": {"type": "integer", "example": 2}, "username": {"type": "string", "example": "jane_doe"}, "full_name": {"type": "string", "example": "<PERSON>"}, "avatar": {"type": "string", "example": "http://example.com/avatar2.jpg", "nullable": true}}, "type": "object"}}, "type": "object"}}, "stats": {"properties": {"total_replies": {"type": "integer", "example": 8}, "total_views": {"type": "integer", "example": 151}, "created_date": {"type": "string", "example": "2024-12-20"}, "last_activity": {"type": "string", "example": "2024-12-20T11:30:00.000000Z"}}, "type": "object"}}, "type": "object"}}, "type": "object"}}}}, "404": {"description": "<PERSON>hr<PERSON> không tồn tại", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Thread không tồn tại hoặc đã bị xóa"}, "data": {"type": "null", "example": null}}, "type": "object"}}}}, "500": {"description": "Lỗi server", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Có lỗi xảy ra khi tải thread"}, "data": {"type": "null", "example": null}}, "type": "object"}}}}}}, "put": {"tags": ["Forum"], "summary": "<PERSON><PERSON><PERSON> nh<PERSON>t thread", "description": "<PERSON><PERSON>p nhật thread (chỉ tác giả)", "operationId": "79ee2fbe1addb9fae4eeaf417c99624e", "parameters": [{"name": "slug", "in": "path", "description": "Slug của thread", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"title": {"type": "string", "example": "Ti<PERSON><PERSON> đề đã cập nh<PERSON>t"}, "content": {"type": "string", "example": "<PERSON>ội dung đã được chỉnh sửa"}}, "type": "object"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nh<PERSON>t thành công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Thread đã đ<PERSON><PERSON><PERSON> cậ<PERSON> nh<PERSON>t"}}, "type": "object"}}}}, "403": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Bạn không có quyền chỉnh sửa thread này"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}, "delete": {"tags": ["Forum"], "summary": "Xóa thread", "description": "<PERSON><PERSON>a thread (chỉ tác giả)", "operationId": "dc87635f4fa1da9d14d6874640dd6da9", "parameters": [{"name": "slug", "in": "path", "description": "Slug của thread", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thành công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Thread đã đ<PERSON><PERSON><PERSON> x<PERSON>a"}}, "type": "object"}}}}, "403": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Bạn không có quyền xóa thread này"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/app/forum/reply": {"post": {"tags": ["Forum"], "summary": "T<PERSON><PERSON> lời thread", "description": "<PERSON><PERSON><PERSON> reply cho thread", "operationId": "b20d0dbbd2d3e13113e0f746e69772fb", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["thread_id", "content"], "properties": {"thread_id": {"type": "integer", "example": 1}, "content": {"type": "string", "example": "<PERSON><PERSON><PERSON> hồi của tôi cho chủ đề này"}, "parent_id": {"description": "ID reply cha (cho nested reply)", "type": "integer", "example": null, "nullable": true}}, "type": "object"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> lời thành công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> lời thành công"}, "data": {"properties": {"reply": {"properties": {"id": {"type": "integer", "example": 1}, "content": {"type": "string", "example": "<PERSON><PERSON><PERSON> hồi của tôi..."}, "thread_id": {"type": "integer", "example": 1}, "parent_id": {"type": "integer", "example": null, "nullable": true}}, "type": "object"}}, "type": "object"}}, "type": "object"}}}}, "401": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "<PERSON><PERSON> lòng đăng nh<PERSON>p"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/app/forum/reply/{replyId}": {"put": {"tags": ["Forum"], "summary": "<PERSON><PERSON><PERSON> reply", "description": "<PERSON><PERSON><PERSON> n<PERSON> reply (chỉ tác giả)", "operationId": "954b2cf84a3ae9bd51b9496bd1e373df", "parameters": [{"name": "replyId", "in": "path", "description": "ID của reply", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"content": {"type": "string", "example": "<PERSON>ội dung đã được chỉnh sửa"}}, "type": "object"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nh<PERSON>t thành công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON>ly đ<PERSON> đ<PERSON><PERSON><PERSON> cậ<PERSON> nh<PERSON>t"}}, "type": "object"}}}}, "403": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "<PERSON><PERSON>n không có quyền chỉnh sửa reply này"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}, "delete": {"tags": ["Forum"], "summary": "<PERSON><PERSON><PERSON> reply", "description": "<PERSON><PERSON><PERSON> reply (chỉ tác giả)", "operationId": "ce388d01d3f56b4b4a1b8fd6681adfc3", "parameters": [{"name": "replyId", "in": "path", "description": "ID của reply", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thành công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON>ly đ<PERSON> đ<PERSON><PERSON><PERSON> x<PERSON>"}}, "type": "object"}}}}, "403": {"description": "<PERSON><PERSON><PERSON><PERSON> có quyền", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "<PERSON><PERSON>n không có quyền xóa reply này"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/app/forum/user/threads": {"get": {"tags": ["Forum"], "summary": "Threads của user", "description": "<PERSON><PERSON><PERSON> s<PERSON>ch threads của user hi<PERSON><PERSON> tại", "operationId": "52da0aaebdf82d886536c4108dde10b7", "parameters": [{"name": "page", "in": "query", "description": "Số trang", "required": false, "schema": {"type": "integer", "default": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"properties": {"threads": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "<PERSON>hread c<PERSON>a tôi"}, "replies_count": {"type": "integer", "example": 5}, "views": {"type": "integer", "example": 150}}, "type": "object"}}}, "type": "object"}}, "type": "object"}}}}, "401": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "<PERSON><PERSON> lòng đăng nh<PERSON>p"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/app/forum/{slug}/replies": {"get": {"tags": ["Forum"], "summary": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> bình luận con (reply) c<PERSON><PERSON> một comment gốc", "description": "<PERSON><PERSON><PERSON> s<PERSON> reply con theo parent_id trong một thread", "operationId": "e11171855f41dfd9c4da662870ebee55", "parameters": [{"name": "slug", "in": "path", "description": "Slug của thread", "required": true, "schema": {"type": "string", "example": "thao-luan-ve-moi-truong-abc123"}}, {"name": "parent_id", "in": "query", "description": "ID của comment gốc (parent)", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 2}, "content": {"type": "string", "example": "<PERSON><PERSON><PERSON> hồi cho comment gốc"}, "thread_id": {"type": "integer", "example": 1}, "parent_id": {"type": "integer", "example": 1}, "created_at": {"type": "string", "example": "2024-12-20T11:30:00.000000Z"}, "updated_at": {"type": "string", "example": "2024-12-20T11:30:00.000000Z"}, "account": {"properties": {"id": {"type": "integer", "example": 3}, "username": {"type": "string", "example": "user123"}, "full_name": {"type": "string", "example": "User Name"}, "avatar": {"type": "string", "example": null, "nullable": true}}, "type": "object"}}, "type": "object"}}}, "type": "object"}}}}, "400": {"description": "Thi<PERSON>u parent_id", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Thi<PERSON>u parent_id"}}, "type": "object"}}}}, "404": {"description": "<PERSON>hr<PERSON> không tồn tại", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Thread không tồn tại hoặc đã bị xóa"}}, "type": "object"}}}}}}}, "/api/app/forum/create/threads": {"post": {"tags": ["Forum"], "summary": "Tạo thread mới (API riêng)", "description": "<PERSON><PERSON>o mới một thread, yêu cầu đăng nh<PERSON>p (sanctum)", "operationId": "54c1517f387a25e4d3c3c5c7295f25c5", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["title", "content"], "properties": {"title": {"type": "string", "example": "Tiêu đề thread mới"}, "content": {"type": "string", "example": "Nội dung thread tối thiểu 10 ký tự"}}, "type": "object"}}}}, "responses": {"201": {"description": "Tạo thread thành công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Tạo thread thành công!"}, "data": {"properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "Tiêu đề thread mới"}, "slug": {"type": "string", "example": "tieu-de-thread-moi-abc12"}, "content": {"type": "string", "example": "Nội dung thread"}, "account_id": {"type": "integer", "example": 2}, "views": {"type": "integer", "example": 0}, "created_at": {"type": "string", "example": "2024-12-20T10:30:45.000000Z"}, "account": {"properties": {"id": {"type": "integer", "example": 2}, "username": {"type": "string", "example": "user123"}, "full_name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "avatar": {"type": "string", "example": null, "nullable": true}}, "type": "object"}}, "type": "object"}}, "type": "object"}}}}, "401": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Bạn cần đăng nhập!"}}, "type": "object"}}}}, "422": {"description": "Lỗi validate", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "The given data was invalid."}, "errors": {"type": "object"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/app/gifts": {"get": {"tags": ["Gifts"], "summary": "<PERSON><PERSON><PERSON> danh sách các món quà", "description": "<PERSON><PERSON>y danh sách các món quà có thể đổi, chỉ lấy quà đang hoạt động.", "operationId": "c06795860b3216866bf78494e7c23013", "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Túi tote canvas"}, "description": {"type": "string", "example": "<PERSON><PERSON><PERSON> vải thân thiện môi trường"}, "image": {"type": "string", "example": "http://example.com/storage/gifts/tote.jpg"}, "value": {"type": "integer", "example": 100}, "quantity": {"type": "integer", "example": 10}, "total_quantity": {"type": "integer", "example": 100}, "is_active": {"type": "boolean", "example": true}, "available_from": {"type": "string", "example": "2024-07-01 00:00:00"}, "available_until": {"type": "string", "example": "2024-12-31 23:59:59"}}, "type": "object"}}}, "type": "object"}}}}}}}, "/api/app/gifts/redeem": {"post": {"tags": ["Gifts"], "summary": "Đổi quà tặng", "description": "Sử dụng điểm để đổi quà tặng (yêu cầu đăng nhập)", "operationId": "0abe551a43a5c0631624ec54720486d4", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["gift_id", "quantity"], "properties": {"gift_id": {"description": "ID của quà tặng", "type": "integer", "example": 1}, "quantity": {"description": "Số lượng", "type": "integer", "maximum": 10, "minimum": 1, "example": 1}}, "type": "object"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> quà thành công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Đổi quà thành công! Vui lòng chờ admin xác n<PERSON>ận."}, "remaining_points": {"type": "integer", "example": 150}, "gift_name": {"type": "string", "example": "Túi tote canvas"}, "quantity": {"type": "integer", "example": 1}, "points_used": {"type": "integer", "example": 100}}, "type": "object"}}}}, "400": {"description": "<PERSON><PERSON><PERSON><PERSON> thể đổi quà", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON> tích lũy không đủ để đổi quà này!"}}, "type": "object"}}}}, "401": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Vui lòng đăng nhập để đổi quà!"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/app/gifts/history": {"post": {"tags": ["Gifts"], "summary": "<PERSON><PERSON><PERSON> sử đổi quà", "description": "<PERSON><PERSON> l<PERSON>ch sử đổi quà của người dùng (yêu cầu đăng nhập, c<PERSON> phân trang)", "operationId": "5288fa6f9f2ad096775685d04a82c1fe", "parameters": [{"name": "page", "in": "query", "description": "Số trang", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "per_page", "in": "query", "description": "<PERSON><PERSON> bản ghi mỗi trang (tối đa 50)", "required": false, "schema": {"type": "integer", "default": 15, "maximum": 50}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> lịch sử thành công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "gift_name": {"type": "string", "example": "Túi tote canvas"}, "quantity": {"type": "integer", "example": 1}, "point_used": {"type": "integer", "example": 100}, "status": {"type": "string", "example": "pending"}, "created_at": {"type": "string", "example": "2024-12-20 10:30:45"}, "notes": {"type": "string", "example": "<PERSON><PERSON> chú từ admin", "nullable": true}}, "type": "object"}}, "pagination": {"properties": {"current_page": {"type": "integer", "example": 1}, "per_page": {"type": "integer", "example": 15}, "total": {"type": "integer", "example": 42}, "last_page": {"type": "integer", "example": 3}, "has_more": {"type": "boolean", "example": true}}, "type": "object"}}, "type": "object"}}}}, "401": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/app/leaderboard/top": {"get": {"tags": ["Leaderboard"], "summary": "Get top members with highest points", "description": "Retrieve top members ranked by points (default 10, max 50)", "operationId": "d347525783232865099e244876e64aa3", "parameters": [{"name": "limit", "in": "query", "description": "Number of top members to retrieve (1-50)", "required": false, "schema": {"type": "integer", "default": 10, "maximum": 50, "minimum": 1}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Top members retrieved successfully"}, "data": {"properties": {"leaderboard": {"type": "array", "items": {"properties": {"rank": {"type": "integer", "example": 1}, "id": {"type": "integer", "example": 123}, "username": {"type": "string", "example": "john_doe"}, "full_name": {"type": "string", "example": "<PERSON>"}, "avatar": {"type": "string", "example": "http://example.com/avatar.jpg", "nullable": true}, "points": {"type": "integer", "example": 1500}, "member_since": {"type": "string", "example": "2024-01-15"}, "member_since_formatted": {"type": "string", "example": "3 months ago"}}, "type": "object"}}, "total_active_members": {"type": "integer", "example": 250}, "showing_top": {"type": "integer", "example": 10}, "last_updated": {"type": "string", "example": "2024-12-20 10:30:45"}}, "type": "object"}}, "type": "object"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "data": {"type": "null"}, "message": {"type": "string", "example": "Failed to retrieve leaderboard"}}, "type": "object"}}}}}}}, "/api/app/leaderboard/member/{accountId}": {"get": {"tags": ["Leaderboard"], "summary": "Get member rank by ID", "description": "Get specific member's rank and details", "operationId": "e941b3543456a208bcd5c14155175595", "parameters": [{"name": "accountId", "in": "path", "description": "Account ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Member rank retrieved successfully"}, "data": {"properties": {"id": {"type": "integer", "example": 123}, "username": {"type": "string", "example": "john_doe"}, "full_name": {"type": "string", "example": "<PERSON>"}, "avatar": {"type": "string", "example": "http://example.com/avatar.jpg", "nullable": true}, "points": {"type": "integer", "example": 1500}, "rank": {"type": "integer", "example": 5}, "member_since": {"type": "string", "example": "2024-01-15"}, "member_since_formatted": {"type": "string", "example": "3 months ago"}}, "type": "object"}}, "type": "object"}}}}, "404": {"description": "Member not found", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "data": {"type": "null"}, "message": {"type": "string", "example": "Member not found or inactive"}}, "type": "object"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "data": {"type": "null"}, "message": {"type": "string", "example": "Failed to retrieve member rank"}}, "type": "object"}}}}}}}, "/api/app/map": {"get": {"tags": ["Map"], "summary": "<PERSON><PERSON><PERSON> danh sách điểm rác trên bản đồ", "description": "<PERSON><PERSON><PERSON> tất cả các điểm báo cáo rác để hiển thị trên bản đồ", "operationId": "65d8b9896958e1a86b3d34492f3fac0d", "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "description": {"type": "string", "example": "<PERSON><PERSON><PERSON> thải tại công viên"}, "latitude": {"type": "number", "format": "float", "example": 21.0285}, "longitude": {"type": "number", "format": "float", "example": 105.8542}, "image": {"type": "string", "example": "http://example.com/storage/reports/image.jpg"}, "status": {"type": "string", "example": "pending"}, "created_at": {"type": "string", "example": "2025-07-25 03:26:12"}}, "type": "object"}}, "message": {"type": "string", "example": "<PERSON><PERSON> liệu bản đồ đ<PERSON><PERSON><PERSON> lấy thành công"}}, "type": "object"}}}}, "500": {"description": "Lỗi server", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "<PERSON><PERSON> lỗi xảy ra"}}, "type": "object"}}}}}}}, "/api/app/notifications": {"post": {"tags": ["Notifications"], "summary": "<PERSON><PERSON><PERSON> danh sách thông báo của user", "description": "<PERSON><PERSON><PERSON> danh sách thông báo của user hi<PERSON><PERSON> tạ<PERSON>, c<PERSON> phân trang chuẩ<PERSON>. <PERSON><PERSON><PERSON> cầu đăng nhập (sanctum)", "operationId": "a162fdf0eb884d0589a6b818174ae84a", "parameters": [{"name": "page", "in": "query", "description": "Số trang", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "per_page", "in": "query", "description": "<PERSON><PERSON> thông báo mỗi trang (tối đa 50)", "required": false, "schema": {"type": "integer", "default": 15, "maximum": 50}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "title": {"type": "string", "example": "Báo cáo đã đư<PERSON><PERSON>!"}, "body": {"type": "string", "example": "Báo cáo: ... đã đư<PERSON><PERSON> phê duyệt và cộng điểm."}, "is_read": {"type": "integer", "example": 0}, "created_at": {"type": "string", "example": "2024-07-01 10:00:00"}}, "type": "object"}}, "pagination": {"properties": {"current_page": {"type": "integer", "example": 1}, "per_page": {"type": "integer", "example": 15}, "total": {"type": "integer", "example": 20}, "last_page": {"type": "integer", "example": 2}, "has_more": {"type": "boolean", "example": false}}, "type": "object"}}, "type": "object"}}}}, "401": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Bạn cần đăng nhập!"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/app/notifications/{id}/read": {"post": {"tags": ["Notifications"], "summary": "<PERSON><PERSON><PERSON> dấu thông báo đã đọc", "description": "<PERSON><PERSON><PERSON> dấu một thông báo là đã đọc cho user hiện tại. <PERSON><PERSON><PERSON> cầu đăng nh<PERSON>p (sanctum)", "operationId": "87f404f35e41fbf1272a5e0c4d818867", "parameters": [{"name": "id", "in": "path", "description": "<PERSON> của thông báo", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Đã đánh dấu là đã đọc!"}, "data": {"properties": {"id": {"type": "integer", "example": 1}, "is_read": {"type": "boolean", "example": true}}, "type": "object"}}, "type": "object"}}}}, "401": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Bạn cần đăng nhập!"}}, "type": "object"}}}}, "404": {"description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thông báo", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Không tìm thấy thông báo!"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/app/notifications/has-unread": {"post": {"tags": ["Notifications"], "summary": "<PERSON><PERSON><PERSON> tra <PERSON> có thông báo chưa đọc không", "description": "<PERSON><PERSON><PERSON> về true nếu user hi<PERSON><PERSON> tại có ít nhất 1 thông bá<PERSON> ch<PERSON> đ<PERSON>, ng<PERSON><PERSON><PERSON> lại trả về false. <PERSON><PERSON><PERSON> cầu đăng nh<PERSON> (sanctum)", "operationId": "dab90afede9246bba75be647f051589c", "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "has_unread": {"type": "boolean", "example": true}}, "type": "object"}}}}, "401": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Bạn cần đăng nhập!"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/app/points/history-points-add": {"post": {"tags": ["Points"], "summary": "<PERSON><PERSON><PERSON> sử cộng điểm", "description": "<PERSON><PERSON><PERSON> lị<PERSON> sử các lần cộng điểm của người dùng (yêu cầu đăng nh<PERSON>, c<PERSON> phân trang)", "operationId": "7d9dd7754929eab6f3134d0d43b4fd45", "parameters": [{"name": "page", "in": "query", "description": "Số trang", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "per_page", "in": "query", "description": "<PERSON><PERSON> bản ghi mỗi trang (tối đa 50)", "required": false, "schema": {"type": "integer", "default": 15, "maximum": 50}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "change": {"type": "integer", "example": 5}, "description": {"type": "string", "example": "<PERSON><PERSON><PERSON> điểm khi đăng nhập"}, "created_at": {"type": "string", "example": "2024-12-20 10:30:45"}}, "type": "object"}}, "pagination": {"properties": {"current_page": {"type": "integer", "example": 1}, "per_page": {"type": "integer", "example": 15}, "total": {"type": "integer", "example": 42}, "last_page": {"type": "integer", "example": 3}}, "type": "object"}}, "type": "object"}}}}, "401": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/app/profile/stats": {"get": {"tags": ["Profile"], "summary": "<PERSON><PERSON><PERSON> thống kê hoạt động của user", "description": "<PERSON><PERSON><PERSON> thống kê số lần b<PERSON><PERSON> c<PERSON>, sự kiện tham gia, sự kiện tạo, b<PERSON><PERSON> viết đã tạo của user đang đăng nhập", "operationId": "869c50e7a28c8a5d5679e0bb0ddc5ca8", "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thống kê thành công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thống kê hoạt động thành công!"}, "data": {"properties": {"reports_count": {"description": "Số lần báo c<PERSON>o rác", "type": "integer", "example": 15}, "events_joined_count": {"description": "Số sự kiện đã tham gia", "type": "integer", "example": 8}, "events_created_count": {"description": "Số sự kiện đã tạo", "type": "integer", "example": 3}, "threads_created_count": {"description": "Số bài viết forum đã tạo", "type": "integer", "example": 12}, "replies_count": {"description": "Số phản hồi trong forum", "type": "integer", "example": 25}, "gifts_redeemed_count": {"description": "Số quà đã đổi", "type": "integer", "example": 4}, "total_points_earned": {"description": "Tổng điểm đã kiếm được", "type": "integer", "example": 500}, "total_points_spent": {"description": "Tổng điểm đã tiêu", "type": "integer", "example": 250}}, "type": "object"}}, "type": "object"}}}}, "401": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "<PERSON>ui lòng đăng nhập để xem thống kê!"}}, "type": "object"}}}}, "500": {"description": "Lỗi server", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Có lỗi xảy ra khi lấy thống kê người dùng!"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/app/profile/me": {"get": {"tags": ["Profile"], "summary": "<PERSON><PERSON><PERSON> thông tin user đang đăng nhập", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của user đang đăng nhập", "operationId": "d5ee2629c7f02f9ee1e64f5d30b510a2", "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thông tin thành công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON>y thông tin người dùng thành công!"}, "data": {"properties": {"id": {"type": "integer", "example": 1}, "username": {"type": "string", "example": "john_doe"}, "email": {"type": "string", "example": "<EMAIL>"}, "full_name": {"type": "string", "example": "<PERSON>"}, "phone_number": {"type": "string", "example": "0123456789", "nullable": true}, "address": {"type": "string", "example": "<PERSON><PERSON>", "nullable": true}, "avatar": {"type": "string", "example": "http://example.com/storage/avatars/avatar.jpg", "nullable": true}, "point": {"type": "integer", "example": 250}, "is_active": {"type": "boolean", "example": true}, "email_verified": {"type": "boolean", "example": true}, "email_verified_at": {"type": "string", "example": "2024-01-15T10:30:45.000000Z", "nullable": true}, "created_at": {"type": "string", "example": "2024-01-15T10:30:45.000000Z"}, "updated_at": {"type": "string", "example": "2024-12-20T10:30:45.000000Z"}, "member_since": {"type": "string", "example": "2024-01-15"}, "member_since_formatted": {"type": "string", "example": "11 tháng tr<PERSON><PERSON>"}}, "type": "object"}}, "type": "object"}}}}, "401": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Vui lòng đăng nhập để xem thông tin!"}}, "type": "object"}}}}, "500": {"description": "Lỗi server", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Có lỗi xảy ra khi lấy thông tin người dùng!"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/app/profile/update-profile": {"post": {"tags": ["Profile"], "summary": "<PERSON><PERSON><PERSON> nhật thông tin cá nhân", "description": "<PERSON><PERSON><PERSON> nh<PERSON>t thông tin hồ sơ người dùng (h<PERSON> tên, số điện thoại, đị<PERSON> chỉ, avatar)", "operationId": "3b422f4692dc3d2475de2cf88ec735e4", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"required": ["full_name"], "properties": {"full_name": {"description": "Họ và tên", "type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "phone_number": {"description": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "type": "string", "example": "0123456789"}, "address": {"description": "Địa chỉ", "type": "string", "example": "<PERSON><PERSON>"}, "avatar": {"description": "Ảnh đại <PERSON> (file ảnh)", "type": "string", "format": "binary"}}, "type": "object"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> nh<PERSON>t thành công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> nhật thông tin thành công!"}, "user": {"properties": {"id": {"type": "integer", "example": 1}, "full_name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "phone_number": {"type": "string", "example": "0123456789"}, "address": {"type": "string", "example": "<PERSON><PERSON>"}, "avatar": {"type": "string", "example": "http://example.com/storage/avatars/avatar.jpg"}}, "type": "object"}}, "type": "object"}}}}, "401": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Bạn chưa đăng nhập!"}}, "type": "object"}}}}, "422": {"description": "<PERSON><PERSON> li<PERSON><PERSON> không hợp lệ", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "<PERSON><PERSON> li<PERSON><PERSON> không hợp lệ"}, "errors": {"type": "object"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/register/email": {"post": {"tags": ["Authentication"], "summary": "G<PERSON>i mã xác thực email", "description": "Bước 1: <PERSON><PERSON><PERSON> mã xác thực 6 số về email để đăng ký tài kho<PERSON>n", "operationId": "1a6f44adddfe78e109bbe99df5713d0d", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["email"], "properties": {"email": {"description": "<PERSON><PERSON>ý", "type": "string", "format": "email", "example": "<EMAIL>"}}, "type": "object"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> mã thành công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON>ã xác thực đã đư<PERSON>c gửi về email"}}, "type": "object"}}}}, "422": {"description": "<PERSON><PERSON> đã tồn tại hoặc không hợp lệ", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "<PERSON>ail đã đư<PERSON><PERSON> đăng ký"}, "errors": {"type": "object"}}, "type": "object"}}}}}}}, "/api/register/verify": {"post": {"tags": ["Authentication"], "summary": "<PERSON><PERSON><PERSON> thực mã email", "description": "Bước 2: <PERSON><PERSON><PERSON> thực mã 6 số đã gửi về email", "operationId": "b29fd030cc1d703ed505d3fc294c6100", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["email", "code"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "code": {"description": "Mã x<PERSON>c thực 6 số", "type": "string", "example": "123456"}}, "type": "object"}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON> thực thành công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> thực thành công"}}, "type": "object"}}}}, "400": {"description": "<PERSON><PERSON> không đúng hoặc đã hết hạn", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "<PERSON>ã xác thực không đúng hoặc đã hết hạn"}}, "type": "object"}}}}}}}, "/api/register/complete": {"post": {"tags": ["Authentication"], "summary": "<PERSON><PERSON><PERSON> tất đăng ký", "description": "Bước 3: <PERSON><PERSON><PERSON> tất đăng ký tài khoản với thông tin cá nhân", "operationId": "b71016ca6718c4c968b219cd546adda2", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["email", "password", "confirm_password", "full_name"], "properties": {"email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"description": "<PERSON><PERSON><PERSON>", "type": "string", "example": "123456"}, "confirm_password": {"description": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "type": "string", "example": "123456"}, "full_name": {"description": "Họ và tên", "type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}}, "type": "object"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> ký thành công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> ký thành công"}, "user": {"properties": {"id": {"type": "integer", "example": 1}, "email": {"type": "string", "example": "<EMAIL>"}, "username": {"type": "string", "example": "user"}, "full_name": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON>"}}, "type": "object"}}, "type": "object"}}}}, "400": {"description": "<PERSON><PERSON><PERSON> x<PERSON>c thực email hoặc dữ liệu không hợp lệ", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "<PERSON><PERSON> lòng x<PERSON>c thực email trước"}}, "type": "object"}}}}}}}, "/api/app/reports/create-report": {"post": {"tags": ["Reports"], "summary": "<PERSON><PERSON><PERSON> báo cáo điểm rác (app)", "description": "<PERSON><PERSON><PERSON> báo cáo điểm rác với hình <PERSON>nh, mô tả, tọa độ GPS (dành cho mobile app)", "operationId": "3d71e0fbed82db7044cda5006e7ed648", "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"required": ["image", "description", "latitude", "longitude"], "properties": {"image": {"description": "<PERSON><PERSON><PERSON>nh điểm rác", "type": "string", "format": "binary"}, "description": {"description": "<PERSON><PERSON> tả chi tiết", "type": "string", "example": "<PERSON><PERSON><PERSON> thải tại công viên"}, "latitude": {"description": "<PERSON><PERSON>", "type": "number", "format": "float", "example": 21.0285}, "longitude": {"description": "Kinh độ GPS", "type": "number", "format": "float", "example": 105.8542}, "gmap_link": {"description": "Link Google Maps (t<PERSON><PERSON> ch<PERSON>n)", "type": "string", "example": "https://maps.google.com/..."}}, "type": "object"}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> đ<PERSON><PERSON><PERSON> gửi thành công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON>áo cáo đã đư<PERSON><PERSON> gửi thành công!"}, "report_id": {"type": "integer", "example": 123}}, "type": "object"}}}}, "401": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Bạn chưa đăng nhập!"}}, "type": "object"}}}}, "422": {"description": "<PERSON><PERSON> li<PERSON><PERSON> không hợp lệ", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "<PERSON><PERSON> li<PERSON><PERSON> không hợp lệ"}, "errors": {"type": "object"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/app/reports/history": {"post": {"tags": ["Reports"], "summary": "<PERSON><PERSON><PERSON> sử báo c<PERSON>o rác", "description": "<PERSON><PERSON><PERSON> lị<PERSON> sử các báo cáo rác của ngư<PERSON>i dùng (yêu cầu đăng nhập, c<PERSON> phân trang)", "operationId": "918ee1916451648bbe5d645669ee473e", "parameters": [{"name": "page", "in": "query", "description": "Số trang", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "per_page", "in": "query", "description": "<PERSON><PERSON> bản ghi mỗi trang (tối đa 50)", "required": false, "schema": {"type": "integer", "default": 15, "maximum": 50}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON> lịch sử thành công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "description": {"type": "string", "example": "<PERSON><PERSON><PERSON> thải tại công viên"}, "image": {"type": "string", "example": "http://example.com/storage/reports/image.jpg"}, "latitude": {"type": "number", "format": "float", "example": 21.0285}, "longitude": {"type": "number", "format": "float", "example": 105.8542}, "gmap_link": {"type": "string", "example": "https://maps.google.com/...", "nullable": true}, "status": {"type": "string", "example": "pending"}, "created_at": {"type": "string", "example": "2024-12-20 10:30:45"}}, "type": "object"}}, "pagination": {"properties": {"current_page": {"type": "integer", "example": 1}, "per_page": {"type": "integer", "example": 15}, "total": {"type": "integer", "example": 42}, "last_page": {"type": "integer", "example": 3}, "has_more": {"type": "boolean", "example": true}}, "type": "object"}}, "type": "object"}}}}, "401": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}, "/api/app/reports/stats": {"post": {"tags": ["Reports"], "summary": "<PERSON>h<PERSON><PERSON> kê báo cáo và đổi quà của user", "description": "Tr<PERSON> về các chỉ số: tổ<PERSON> b<PERSON><PERSON> c<PERSON>, bá<PERSON> c<PERSON><PERSON> đã <PERSON>, đang chờ, bị từ chối, tổng lần đổi quà, đổi quà đã du<PERSON>, đã ho<PERSON> thành, tổng điểm đã tích.", "operationId": "a394f60f7885d252b9eaf963ed4d319b", "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> công", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"properties": {"total_reports": {"type": "integer", "example": 20}, "approved_reports": {"type": "integer", "example": 10}, "pending_reports": {"type": "integer", "example": 5}, "rejected_reports": {"type": "integer", "example": 5}, "total_gift_redemptions": {"type": "integer", "example": 8}, "approved_gift_redemptions": {"type": "integer", "example": 3}, "completed_gift_redemptions": {"type": "integer", "example": 2}, "total_points_earned": {"type": "integer", "example": 500}}, "type": "object"}}, "type": "object"}}}}, "401": {"description": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> đ<PERSON>h<PERSON>p"}}, "type": "object"}}}}}, "security": [{"sanctum": []}]}}}, "components": {"securitySchemes": {"sanctum": {"type": "http", "description": "Enter token in format (Bearer <token>)", "bearerFormat": "JWT", "scheme": "bearer"}}}, "tags": [{"name": "Authentication", "description": "Authentication endpoints"}, {"name": "Leaderboard", "description": "Leaderboard and ranking endpoints"}, {"name": "Forum", "description": "Forum and discussion endpoints"}, {"name": "Map", "description": "Map and location endpoints"}, {"name": "Reports", "description": "Report submission endpoints"}, {"name": "Gifts", "description": "Gift redemption endpoints"}, {"name": "Profile", "description": "User profile management endpoints"}, {"name": "Events", "description": "Event management endpoints"}, {"name": "Notifications", "description": "Notifications"}, {"name": "Points", "description": "Points"}]}