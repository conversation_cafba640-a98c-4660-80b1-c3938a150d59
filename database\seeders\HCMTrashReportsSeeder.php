<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Report;
use App\Models\Account;

class HCMTrashReportsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * Tạo 10 điểm đổ rác demo tại TP.HCM với status approved
     */
    public function run(): void
    {
        // Xóa tất cả report cũ
        Report::truncate();
        $this->command->info('Đã xóa tất cả báo cáo cũ');

        // Đảm bảo có ít nhất 1 account để làm creator
        if (Account::count() === 0) {
            Account::factory()->create([
                'name' => 'Demo User',
                'email' => '<EMAIL>',
            ]);
        }

        // Tạo 10 điểm đổ rác cụ thể tại TP.HCM
        $hcmTrashReports = [
            [
                'latitude' => 10.7769,
                'longitude' => 106.7009,
                'description' => 'Điểm đổ rác gần chợ <PERSON>ế<PERSON>, quận 1 - <PERSON><PERSON><PERSON> thải sinh hoạt tập trung nhiều, cần dọn dẹp gấp',
                'gmap_link' => 'https://maps.google.com/?q=10.7769,106.7009',
            ],
            [
                'latitude' => 10.7625,
                'longitude' => 106.6820,
                'description' => 'Khu vực đổ rác ven kênh Nhiêu Lộc, quận 3 - Rác thải nhựa và túi nilon nhiều, ảnh hưởng đến môi trường nước',
                'gmap_link' => 'https://maps.google.com/?q=10.7625,106.6820',
            ],
            [
                'latitude' => 10.8142,
                'longitude' => 106.6438,
                'description' => 'Điểm tập kết rác tại quận Tân Bình - Khu vực có nhiều túi rác bỏ bừa bãi gần khu dân cư',
                'gmap_link' => 'https://maps.google.com/?q=10.8142,106.6438',
            ],
            [
                'latitude' => 10.7308,
                'longitude' => 106.7175,
                'description' => 'Khu đổ rác gần cầu Sài Gòn, quận 4 - Điểm đổ rác không đúng quy định, có mùi hôi thối',
                'gmap_link' => 'https://maps.google.com/?q=10.7308,106.7175',
            ],
            [
                'latitude' => 10.7546,
                'longitude' => 106.6677,
                'description' => 'Điểm rác thải ven sông Sài Gòn, quận 5 - Rác thải công nghiệp cần xử lý, ô nhiễm nguồn nước',
                'gmap_link' => 'https://maps.google.com/?q=10.7546,106.6677',
            ],
            [
                'latitude' => 10.7429,
                'longitude' => 106.6509,
                'description' => 'Khu vực đổ rác tại quận 6 - Khu vực ô nhiễm môi trường nghiêm trọng, cần can thiệp khẩn cấp',
                'gmap_link' => 'https://maps.google.com/?q=10.7429,106.6509',
            ],
            [
                'latitude' => 10.7335,
                'longitude' => 106.6181,
                'description' => 'Điểm tập trung rác thải quận 8 - Điểm tập kết rác cần dọn dẹp gấp, ảnh hưởng đến sinh hoạt người dân',
                'gmap_link' => 'https://maps.google.com/?q=10.7335,106.6181',
            ],
            [
                'latitude' => 10.8693,
                'longitude' => 106.8037,
                'description' => 'Khu đổ rác tại quận 9 (Thủ Đức) - Rác thải sinh hoạt tập trung nhiều ở khu vực mới phát triển',
                'gmap_link' => 'https://maps.google.com/?q=10.8693,106.8037',
            ],
            [
                'latitude' => 10.8506,
                'longitude' => 106.7717,
                'description' => 'Điểm rác thải khu công nghiệp Thủ Đức - Rác thải công nghiệp và sinh hoạt lẫn lộn, cần phân loại',
                'gmap_link' => 'https://maps.google.com/?q=10.8506,106.7717',
            ],
            [
                'latitude' => 10.7215,
                'longitude' => 106.6291,
                'description' => 'Khu vực đổ rác ven kênh quận 7 - Rác thải nhựa và túi nilon nhiều, cần dọn dẹp và tuyên truyền',
                'gmap_link' => 'https://maps.google.com/?q=10.7215,106.6291',
            ],
        ];

        $accountId = Account::inRandomOrder()->first()->id;

        foreach ($hcmTrashReports as $reportData) {
            Report::create([
                'created_by' => $accountId,
                'image' => 'reports/images/demo_' . uniqid() . '.jpg',
                'description' => $reportData['description'],
                'latitude' => $reportData['latitude'],
                'longitude' => $reportData['longitude'],
                'gmap_link' => $reportData['gmap_link'],
                'status' => 'approved', // Tất cả đều được approved
                'created_at' => now()->subDays(rand(1, 30)), // Tạo thời gian ngẫu nhiên trong 30 ngày qua
                'updated_at' => now()->subDays(rand(0, 5)), // Cập nhật gần đây hơn
            ]);
        }

        $this->command->info('Đã tạo thành công 10 điểm đổ rác demo tại TP.HCM - tất cả đều approved!');
        
        // Thống kê
        $totalReports = Report::count();
        $approvedReports = Report::where('status', 'approved')->count();
        
        $this->command->info("Tổng số báo cáo: {$totalReports}");
        $this->command->info("Báo cáo đã duyệt: {$approvedReports}");
    }
}
