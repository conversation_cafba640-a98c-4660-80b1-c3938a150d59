<?php

use App\Http\Controllers\ProfileController;
use App\Models\Language;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;
use Illuminate\Foundation\Auth\EmailVerificationRequest;

use App\Http\Controllers\DebugController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\PostController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\EventController;
use App\Http\Controllers\ForumController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\DashboardController;
use Illuminate\Support\Facades\Session;

// Redirect dashboard without locale to dashboard with locale
Route::get('/dashboard', function () {
    $locale = session('locale', 'vi');
    return redirect("/{$locale}/dashboard");
});

Route::group(['prefix' => '{locale}', 'where' => ['locale' => '[a-zA-Z]{2}']], function () {

// Webhook routes
    Route::post('/webhook', [DebugController::class, 'index'])->name('webhook');

// Home routes
    Route::get('/', [HomeController::class, 'index'])->name('home');

// Auth routes
    Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AuthController::class, 'login'])->name('login.post');
    Route::get('/reset-password', [AuthController::class, 'showResetPassword'])->name('reset-password');
    Route::post('/reset-password', [AuthController::class, 'resetPassword'])->name('reset-password.post');
    Route::get('/dashboard/verification-notice', function () {
        return view('auth.verify-email');
    })->middleware('auth:account')->name('verification.notice');
    Route::post('/email/verification-notification', function (Request $request) {
        $request->user('account')->sendEmailVerificationNotification();
        return back()->with('status', 'verification-link-sent');
    })->middleware(['auth:account', 'throttle:6,1'])->name('verification.send');
    Route::get('/email/verify/{id}/{hash}', function (EmailVerificationRequest $request) {
        $request->fulfill();
        return redirect('/dashboard');
    })->middleware(['auth:account', 'signed', 'throttle:6,1'])->name('verification.verify');
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
// Category routes
    Route::get('/categories', [CategoryController::class, 'index'])->name('categories.index');
    Route::get('/categories/{slug}', [CategoryController::class, 'show'])->name('categories.show');

// Post routes
    Route::get('/posts', [PostController::class, 'index'])->name('posts.index');
    Route::get('/posts/{slug}', [PostController::class, 'show'])->name('posts.show');

// Page routes
    Route::get('/report', [PageController::class, 'report'])->name('pages.report');
    Route::post('/report', [PageController::class, 'submitReport'])->middleware('auth:account')->name('pages.report.submit');
    Route::get('/map', [PageController::class, 'map'])->name('pages.map');
    Route::get('/about', [PageController::class, 'about'])->name('pages.about');
    Route::get('/contact', [PageController::class, 'contact'])->name('pages.contact');
    Route::post('/contact', [PageController::class, 'submitContact'])->name('pages.contact.submit');

// Event routes
    Route::get('/event', [EventController::class, 'event'])->name('pages.event');
    Route::get('/event/{slug}', [EventController::class, 'show'])->name('event.show');

// Forum routes
    Route::get('/forum', [ForumController::class, 'index'])->name('forum.index');
    Route::get('/forum/create', [ForumController::class, 'create'])->name('forum.create');
    Route::post('/forum/store', [ForumController::class, 'store'])->name('forum.store');
    Route::get('/forum/thread/{slug}', [ForumController::class, 'show'])->name('forum.show');
    Route::post('/forum/thread/{slug}/reply', [ForumController::class, 'reply'])->name('forum.reply');

    Route::get('/dashboard', [DashboardController::class, 'index'])->middleware(['auth:account', 'account.verified'])->name('dashboard');
    Route::get('/dashboard/edit', [DashboardController::class, 'edit'])->middleware(['auth:account', 'account.verified'])->name('dashboard.edit');
    Route::post('/dashboard/update', [DashboardController::class, 'update'])->middleware(['auth:account', 'account.verified'])->name('dashboard.update');

// Dashboard routes
    Route::prefix('dashboard')->middleware(['auth:account', 'account.verified'])->group(function () {
        Route::get('/statistics', [DashboardController::class, 'statistics'])->name('dashboard.statistics');
        Route::get('/profile', [DashboardController::class, 'profile'])->name('dashboard.profile');
        Route::get('/gifts', [DashboardController::class, 'gifts'])->name('dashboard.gifts');
        Route::get('/events', [DashboardController::class, 'myEvents'])->name('dashboard.events');
        Route::get('/events/create', [DashboardController::class, 'createEventForm'])->name('dashboard.events.create');
        Route::post('/events', [DashboardController::class, 'storeEvent'])->name('dashboard.events.store');
    });
});

Route::get('/', function () {
    // Nếu session có locale → ưu tiên session
    if (Session::has('locale')) {
        $locale = Session::get('locale');
    } else {
        // Nếu không thì lấy default từ DB (và cache lại)
        $locale = cache()->rememberForever('default_locale', function () {
            return Language::where('is_default', true)->value('locale') ?? config('app.locale');
        });
    }

    return redirect()->to("/{$locale}");
});

Route::post('/set-locale', function (Request $request) {
    $locale = $request->input('locale', 'en');

    $availableLocales = cache()->rememberForever('available_locales', function () {
        return Language::where('is_active', true)->pluck('locale')->toArray();
    });

    if (!in_array($locale, $availableLocales)) {
        $locale = cache()->rememberForever('default_locale', function () {
            return Language::where('is_default', true)->value('locale') ?? config('app.locale');
        });
    }

    Session::put('locale', $locale);
    App::setLocale($locale);

    // Get the referer URL and replace locale
    $referer = $request->header('referer');
    if ($referer) {
        $currentLocale = app()->getLocale();

        // Parse the referer URL
        $parsedUrl = parse_url($referer);
        $path = $parsedUrl['path'] ?? '/';

        // Replace locale in path
        if (preg_match('/^\/([a-z]{2})(\/.*)?$/', $path, $matches)) {
            $newPath = "/{$locale}" . ($matches[2] ?? '');
        } else {
            $newPath = "/{$locale}" . ($path === '/' ? '' : $path);
        }

        return redirect()->to($newPath);
    }

    return redirect()->to("/{$locale}");
})->name('set-locale');

// Download translations route (outside locale group)
Route::get('/download-translations', function () {
    $filePath = storage_path('app/translations/all_translations.json');

    if (!file_exists($filePath)) {
        \Illuminate\Support\Facades\Artisan::call('translations:export-all');
    }

    return response()->download($filePath, 'all_translations_' . date('Y-m-d_H-i-s') . '.json');
})->name('download.translations');
