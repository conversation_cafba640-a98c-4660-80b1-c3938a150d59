<?php $__env->startSection('title', trans_db('general.danh_sach_su_kien')); ?>
<?php $__env->startSection('header', trans_db('general.danh_sach_su_kien')); ?>
<?php $__env->startSection('content'); ?>
<div class="container mx-auto py-8">
    <h2 class="text-3xl font-bold text-center mb-6 bg-gradient-to-r from-[#10B981] to-[#0EA5E9] bg-clip-text text-transparent"><?php echo e(trans_db('general.danh_sach_su_kien')); ?></h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <?php $__empty_1 = true; $__currentLoopData = $events; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <?php
                $now = \Carbon\Carbon::now();
                $start = \Carbon\Carbon::parse($event->start_date);
                $end = $event->end_date ? \Carbon\Carbon::parse($event->end_date) : null;
                if ($now->lt($start)) {
                    $status = [trans_db('general.sap_dien_ra'), 'bg-yellow-400', 'text-yellow-900'];
                } elseif ($end && $now->gt($end)) {
                    $status = [trans_db('general.ket_thuc'), 'bg-gray-400', 'text-gray-800'];
                } else {
                    $status = [trans_db('general.dang_dien_ra'), 'bg-green-500', 'text-white'];
                }
            ?>
            <a href="<?php echo e(route('event.show', $event->slug)); ?>" class="relative bg-white rounded-2xl shadow-2xl p-4 flex flex-col hover:shadow-lg transition group border border-[#10B981]/20 hover:border-[#0EA5E9]">
                <span class="absolute top-3 right-3 px-3 py-1 rounded-full text-xs font-bold <?php echo e($status[1]); ?> <?php echo e($status[2]); ?>">
                    <?php echo e($status[0]); ?>

                </span>
                <?php if($event->featured_image): ?>
                    <img src="<?php echo e(asset('storage/'.$event->featured_image)); ?>" alt="<?php echo e($event->title); ?>" class="rounded mb-3 w-full h-40 object-cover border border-[#10B981]/30">
                <?php else: ?>
                    <img src="https://placehold.co/600x400?text=No+Image" alt="No Image" class="rounded mb-3 w-full h-40 object-cover border border-[#10B981]/30">
                <?php endif; ?>
                <h3 class="text-lg font-bold bg-gradient-to-r from-[#10B981] to-[#0EA5E9] bg-clip-text text-transparent mb-1 group-hover:underline"><?php echo e($event->title); ?></h3>
                <div class="text-sm text-gray-500 mb-1">
                    <span class="font-medium text-[#10B981]"><?php echo e(trans_db('general.thoi_gian')); ?>:</span>
                    <?php echo e(\Carbon\Carbon::parse($event->start_date)->format('d/m/Y H:i')); ?>

                    <?php if($event->end_date): ?>
                        - <?php echo e(\Carbon\Carbon::parse($event->end_date)->format('d/m/Y H:i')); ?>

                    <?php endif; ?>
                </div>
                <div class="text-sm text-gray-500 mb-2">
                    <span class="font-medium text-[#0EA5E9]"><?php echo e(trans_db('general.dia_diem')); ?>:</span> <?php echo e($event->location); ?>

                </div>
                <?php if($event->max_participants): ?>
                <div class="text-xs text-gray-400 mb-2">
                    <span class="font-medium"><?php echo e(trans_db('general.so_nguoi_toi_da')); ?>:</span> <?php echo e($event->max_participants); ?>

                </div>
                <?php endif; ?>
                <div class="text-gray-700 mb-3"><?php echo e(Str::limit($event->description, 100)); ?></div>
            </a>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="col-span-3 text-center text-gray-500"><?php echo e(trans_db('common.chua_co_su_kien_nao')); ?></div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\PROJECT\backend-app_don_rac\resources\views/pages/event.blade.php ENDPATH**/ ?>