<x-guest-layout>
    <div class="w-full min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-[#0EA5E9]/10 via-[#10B981]/10 to-[#4ADE80]/10 py-12 px-4">
        <div class="w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 md:p-10">
            <h2 class="text-3xl font-bold text-center mb-8 bg-gradient-to-r from-[#10B981] to-[#0EA5E9] bg-clip-text text-transparent">{{ trans_db('nav.dang_nhap') ?? 'Đăng nhập' }}</h2>
            <x-auth-session-status class="mb-4" :status="session('status')" />
            <form method="POST" action="{{ route('login.post', ['locale' => app()->getLocale()]) }}" class="space-y-6" id="login-form">
                @csrf
                <!-- Email Address -->
                <div>
                    <x-input-label for="email" value="{{ trans_db('form.email_hoac_ten_dang_nhap') }}" class="text-[#10B981] font-semibold"></x-input-label>
                    <x-text-input id="email" class="block mt-1 w-full rounded-lg border border-[#10B981]/30 focus:border-[#10B981] focus:ring-[#10B981]" type="text" name="email" :value="old('email')" required autofocus autocomplete="username" placeholder="{{ trans_db('nav.nhap_email_hoac_ten_dang_nhap') }}"></x-text-input>
                    <x-input-error :messages="$errors->get('email')" class="mt-2"></x-input-error>
                </div>
                <!-- Password -->
                <div>
                    <x-input-label for="password" value="{{ trans_db('form.mat_khau') }}" class="text-[#10B981] font-semibold"></x-input-label>
                    <x-text-input id="password" class="block mt-1 w-full rounded-lg border border-[#10B981]/30 focus:border-[#10B981] focus:ring-[#10B981]" type="password" name="password" required autocomplete="current-password" placeholder="{{ trans_db('form.nhap_mat_khau') }}"></x-text-input>
                    <x-input-error :messages="$errors->get('password')" class="mt-2"></x-input-error>
                </div>
                <!-- Remember Me -->
                <div class="flex items-center justify-between">
                    <label for="remember_me" class="inline-flex items-center">
                        <input id="remember_me" type="checkbox" class="rounded border-gray-300 text-[#10B981] shadow-sm focus:ring-[#10B981]" name="remember">
                        <span class="ms-2 text-sm text-gray-600">{{ trans_db('nav.ghi_nho_dang_nhap') ?? 'Ghi nhớ đăng nhập' }}</span>
                    </label>
                    @if (Route::has('password.request'))
                        <a class="text-sm text-[#0EA5E9] hover:text-[#10B981] transition-colors font-medium" href="{{ route('password.request') }}">
                            Quên mật khẩu?
                        </a>
                    @endif
                </div>
                <div>
                    <button type="submit" class="w-full py-3 rounded-lg text-white font-semibold bg-gradient-to-r from-[#10B981] to-[#0EA5E9] hover:from-[#0EA5E9] hover:to-[#10B981] transition duration-300">
                        {{ trans_db('nav.dang_nhap') ?? 'Đăng nhập' }}
                    </button>
                </div>

                <!-- Register Link -->
                <div class="text-center mt-4">
                    <span class="text-gray-600">{{ trans_db('common.chua_co_tai_khoan') ?? 'Chưa có tài khoản?' }}</span>
                    <a href="{{ route('register', ['locale' => app()->getLocale()]) }}" class="text-[#0EA5E9] hover:text-[#10B981] font-medium ml-1 transition duration-300">
                        {{ trans_db('nav.dang_ky') ?? 'Đăng ký' }}
                    </a>
                </div>
            </form>
        </div>
    </div>
</x-guest-layout>
