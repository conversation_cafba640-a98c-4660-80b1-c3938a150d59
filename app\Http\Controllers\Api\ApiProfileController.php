<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class ApiProfileController extends Controller
{
    public function update(Request $request)
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Bạn chưa đăng nhập!'
            ], 401);
        }

        $request->validate([
            'full_name' => 'required|string|max:255',
            'address' => 'nullable|string|max:255',
            'phone_number' => 'nullable|string|max:20',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ]);

        $user->full_name = $request->full_name;
        $user->address = $request->address;
        $user->phone_number = $request->phone_number;

        if ($request->hasFile('avatar')) {
            if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
                Storage::disk('public')->delete($user->avatar);
            }
            $path = $request->file('avatar')->store('avatars', 'public');
            $user->avatar = $path;
        }

        $user->save();

        return response()->json([
            'success' => true,
            'message' => 'Cập nhật thông tin thành công!',
            'avatar_url' => $user->avatar ? asset('storage/' . $user->avatar) : null,
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/app/profile/stats",
     *     tags={"Profile"},
     *     summary="Lấy thống kê hoạt động của user",
     *     description="Lấy thống kê số lần báo cáo, sự kiện tham gia, sự kiện tạo, bài viết đã tạo của user đang đăng nhập",
     *     security={{"sanctum": {}}},
     *     @OA\Response(
     *         response=200,
     *         description="Lấy thống kê thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Lấy thống kê hoạt động thành công!"),
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="reports_count", type="integer", example=15, description="Số lần báo cáo rác"),
     *                 @OA\Property(property="events_joined_count", type="integer", example=8, description="Số sự kiện đã tham gia"),
     *                 @OA\Property(property="events_created_count", type="integer", example=3, description="Số sự kiện đã tạo"),
     *                 @OA\Property(property="threads_created_count", type="integer", example=12, description="Số bài viết forum đã tạo"),
     *                 @OA\Property(property="replies_count", type="integer", example=25, description="Số phản hồi trong forum"),
     *                 @OA\Property(property="gifts_redeemed_count", type="integer", example=4, description="Số quà đã đổi"),
     *                 @OA\Property(property="total_points_earned", type="integer", example=500, description="Tổng điểm đã kiếm được"),
     *                 @OA\Property(property="total_points_spent", type="integer", example=250, description="Tổng điểm đã tiêu")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Chưa đăng nhập",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Vui lòng đăng nhập để xem thống kê!")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Lỗi server",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Có lỗi xảy ra khi lấy thống kê người dùng!")
     *         )
     *     )
     * )
     */
    public function getUserStats(Request $request)
    {
        try {
            // Lấy user từ token (Sanctum authentication)
            $user = $request->user();
            
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Vui lòng đăng nhập để xem thống kê!'
                ], 401);
            }

            // Đếm số lần báo cáo
            $reportsCount = $user->reports()->count();

            // Đếm số sự kiện đã tham gia
            $eventsJoinedCount = $user->eventRegistrations()->count();

            // Đếm số sự kiện đã tạo
            $eventsCreatedCount = $user->createdEvents()->count();

            // Đếm số bài viết forum đã tạo
            $threadsCreatedCount = $user->threads()->count();

            // Đếm số phản hồi trong forum
            $repliesCount = $user->replies()->count();

            // Đếm số quà đã đổi
            $giftsRedeemedCount = $user->giftRedemptions()->count();

            // Tính tổng điểm đã kiếm được và đã tiêu
            $pointHistories = $user->pointHistories();
            $totalPointsEarned = $pointHistories->where('change', '>', 0)->sum('change');
            $totalPointsSpent = abs($pointHistories->where('change', '<', 0)->sum('change'));

            // Chuẩn bị thống kê hoạt động
            $activityStats = [
                'reports_count' => $reportsCount,
                'events_joined_count' => $eventsJoinedCount,
                'events_created_count' => $eventsCreatedCount,
                'threads_created_count' => $threadsCreatedCount,
                'replies_count' => $repliesCount,
                'gifts_redeemed_count' => $giftsRedeemedCount,
                'total_points_earned' => $totalPointsEarned,
                'total_points_spent' => $totalPointsSpent
            ];

            return response()->json([
                'success' => true,
                'message' => 'Lấy thống kê hoạt động thành công!',
                'data' => $activityStats
            ]);

        } catch (\Exception $e) {
            Log::error('Profile Stats API Error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi lấy thống kê người dùng!'
            ], 500);
        }
    }

    
       /**
     * @OA\Get(
     *     path="/api/app/profile/me",
     *     tags={"Profile"},
     *     summary="Lấy thông tin user đang đăng nhập",
     *     description="Lấy thông tin chi tiết của user đang đăng nhập",
     *     security={{"sanctum": {}}},
     *     @OA\Response(
     *         response=200,
     *         description="Lấy thông tin thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Lấy thông tin người dùng thành công!"),
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="username", type="string", example="john_doe"),
     *                 @OA\Property(property="email", type="string", example="<EMAIL>"),
     *                 @OA\Property(property="full_name", type="string", example="John Doe"),
     *                 @OA\Property(property="phone_number", type="string", nullable=true, example="0123456789"),
     *                 @OA\Property(property="address", type="string", nullable=true, example="Hà Nội"),
     *                 @OA\Property(property="avatar", type="string", nullable=true, example="http://example.com/storage/avatars/avatar.jpg"),
     *                 @OA\Property(property="point", type="integer", example=250),
     *                 @OA\Property(property="is_active", type="boolean", example=true),
     *                 @OA\Property(property="email_verified", type="boolean", example=true),
     *                 @OA\Property(property="email_verified_at", type="string", nullable=true, example="2024-01-15T10:30:45.000000Z"),
     *                 @OA\Property(property="created_at", type="string", example="2024-01-15T10:30:45.000000Z"),
     *                 @OA\Property(property="updated_at", type="string", example="2024-12-20T10:30:45.000000Z"),
     *                 @OA\Property(property="member_since", type="string", example="2024-01-15"),
     *                 @OA\Property(property="member_since_formatted", type="string", example="11 tháng trước")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Chưa đăng nhập",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Vui lòng đăng nhập để xem thông tin!")
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Lỗi server",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Có lỗi xảy ra khi lấy thông tin người dùng!")
     *         )
     *     )
     * )
     */
    public function getUserInfo(Request $request)
    {
        try {
            // Lấy user từ token (Sanctum authentication)
            $user = $request->user();
            
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Vui lòng đăng nhập để xem thông tin!'
                ], 401);
            }

            return response()->json([
                'success' => true,
                'message' => 'Lấy thông tin người dùng thành công!',
                'data' => [
                    'id' => $user->id,
                    'username' => $user->username,
                    'email' => $user->email,
                    'full_name' => $user->full_name,
                    'phone_number' => $user->phone_number,
                    'address' => $user->address,
                    'avatar' => $user->avatar ? asset('storage/' . $user->avatar) : null,
                    'point' => $user->point,
                    'is_active' => $user->is_active,
                    'email_verified' => $user->email_verified_at ? true : false,
                    'email_verified_at' => formatDateTime($user->email_verified_at),
                    'created_at' => formatDateTime($user->created_at),
                    'updated_at' => formatDateTime($user->updated_at),
                    'member_since' => $user->created_at ? $user->created_at->format('Y-m-d') : null,
                    'member_since_formatted' => $user->created_at ? $user->created_at->diffForHumans() : null,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Profile User Info API Error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi lấy thông tin người dùng!'
            ], 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/app/profile/update-profile",
     *     tags={"Profile"},
     *     summary="Cập nhật thông tin cá nhân",
     *     description="Cập nhật thông tin hồ sơ người dùng (họ tên, số điện thoại, địa chỉ, avatar)",
     *     security={{"sanctum": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 required={"full_name"},
     *                 @OA\Property(property="full_name", type="string", example="Nguyễn Văn A", description="Họ và tên"),
     *                 @OA\Property(property="phone_number", type="string", example="0123456789", description="Số điện thoại"),
     *                 @OA\Property(property="address", type="string", example="Hà Nội", description="Địa chỉ"),
     *                 @OA\Property(property="avatar", type="string", format="binary", description="Ảnh đại diện (file ảnh)")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Cập nhật thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Cập nhật thông tin thành công!"),
     *             @OA\Property(property="user", type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="full_name", type="string", example="Nguyễn Văn A"),
     *                 @OA\Property(property="phone_number", type="string", example="0123456789"),
     *                 @OA\Property(property="address", type="string", example="Hà Nội"),
     *                 @OA\Property(property="avatar", type="string", example="http://example.com/storage/avatars/avatar.jpg")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Chưa đăng nhập",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Bạn chưa đăng nhập!")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Dữ liệu không hợp lệ",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Dữ liệu không hợp lệ"),
     *             @OA\Property(property="errors", type="object")
     *         )
     *     )
     * )
     */
    public function updateProfile(Request $request)
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Bạn chưa đăng nhập!'
            ], 401);
        }

        $request->validate([
            'full_name' => 'required|string|max:255',
            'address' => 'nullable|string|max:255',
            'phone_number' => 'nullable|string|max:20',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ]);

        $user->full_name = $request->full_name;
        $user->address = $request->address;
        $user->phone_number = $request->phone_number;

        if ($request->hasFile('avatar')) {
            if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
                Storage::disk('public')->delete($user->avatar);
            }
            $path = $request->file('avatar')->store('avatars', 'public');
            $user->avatar = $path;
        }

        $user->save();

        return response()->json([
            'success' => true,
            'message' => 'Cập nhật thông tin thành công!',
            'user' => [
                'id' => $user->id,
                'full_name' => $user->full_name,
                'phone_number' => $user->phone_number,
                'address' => $user->address,
                'avatar' => $user->avatar ? asset('storage/' . $user->avatar) : null,
            ]
        ]);
    }
} 