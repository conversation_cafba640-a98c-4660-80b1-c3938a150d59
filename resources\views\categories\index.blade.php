@extends('layouts.app')

@section('title('general.danh_muc') }}')

@section('header('general.tat_ca_danh_muc') }}')

@section('content')
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        @forelse($categories ?? [] as $category)
            <div class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow overflow-hidden border">
                @if($category->image)
                    <img src="{{ $category->image }}" alt="{{ $category->name }}" class="w-full h-40 object-cover-12 h-12 text-gray-400" fill="none" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z-4">
                    <h3 class="text-lg font-semibold mb-2">
                        <a href="{{ route('categories.show', $category) }}" class="text-indigo-600 hover:text-indigo-800">{{ $category->name }}</a>
                    </h3>
                    @if($category->description)
                        <p class="text-gray-600 mb-3 text-sm justify-between items-center text-sm-500">{{ $category->posts_count ?? 0 }} bài viết</span>
                        <a href="{{ route('categories.show', $category) }}" class="text-indigo-600 hover:text-indigo-800 font-medium') }}
                    </div>
                </div>
            </div>
        @empty
            <div class="col-span-full bg-gray-50 rounded-lg p-6 text-center-500">{{ trans_db('common.chua_co_danh_muc_nao') }}
            </div>
        @endforelse
    </div>

    @if(isset($categories) && $categories->hasPages())
        <div class="mt-6">
            {{ $categories->links() }}
        </div>
    @endif
@endsection