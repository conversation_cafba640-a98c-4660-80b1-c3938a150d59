<?php
// Simple test to check if route generation works
require_once 'vendor/autoload.php';

// Test route generation
echo "Testing route generation...\n";

// Test basic route
try {
    $app = require_once 'bootstrap/app.php';
    $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
    
    // Set locale
    app()->setLocale('vi');
    
    // Test route with slug parameter
    $testSlug = 'test-event-slug';
    $url = route('event.show', ['slug' => $testSlug]);
    echo "Generated URL: " . $url . "\n";
    
    // Test localized route
    if (function_exists('localized_route')) {
        $localizedUrl = localized_route('event.show', ['slug' => $testSlug]);
        echo "Localized URL: " . $localizedUrl . "\n";
    }
    
    echo "Route test completed successfully!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
