<?php

namespace App\Providers;

use App\Services\LanguageService;
use App\View\Components\LangSwitcher;

use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\URL;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register LanguageService
        $this->app->singleton(LanguageService::class, function ($app) {
            return new LanguageService();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Only force HTTPS in production
        if (app()->environment('production')) {
            URL::forceScheme('https');
        }

        // Register Blade components
        Blade::component('lang-switcher', LangSwitcher::class);

        // Register custom Blade directive for smart routing
        Blade::directive('route', function ($expression) {
            return "<?php echo smart_route{$expression}; ?>";
        });

        // Load helpers
        if (file_exists(app_path('helpers.php'))) {
            require_once app_path('helpers.php');
        }
    }
}
