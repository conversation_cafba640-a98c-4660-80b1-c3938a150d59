<x-guest-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ trans_db('general.qua_tang') ?? 'Quà tặng' }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="flex min-h-[400px]">
                    <!-- Sidebar -->
                    @include('dashboard.sidebar')

                    <!-- Main Content -->
                    <div class="w-3/4 p-6">
                        <!-- Hướng dẫn đổi quà -->
                        <div class="mb-8">
                            <h3 class="text-xl font-bold mb-4 text-gray-800">{{ trans_db('general.huong_dan_doi_qua') ?? 'H<PERSON>ớng dẫn đổi quà' }}</h3>
                            <div class="bg-green-50 rounded-lg p-6">
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div class="text-center">
                                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                            <span class="text-green-600 text-2xl">1</span>
                                        </div>
                                        <h4 class="font-semibold text-gray-800 mb-2">{{ trans_db('common.chon_qua') ?? 'Chọn quà' }}</h4>
                                        <p class="text-sm text-gray-600">{{ trans_db('common.chon_qua_tang_phu_hop_voi_so_diem_ban_co') ?? 'Chọn quà tặng phù hợp với số điểm bạn có' }}</p>
                                    </div>
                                    <div class="text-center">
                                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                            <span class="text-blue-600 text-2xl">2</span>
                                        </div>
                                        <h4 class="font-semibold text-gray-800 mb-2">{{ trans_db('general.xac_nhan_doi') ?? 'Xác nhận đổi' }}</h4>
                                        <p class="text-sm text-gray-600">Nhấn "{{ trans_db('general.doi_qua') ?? 'Đổi quà' }}" và xác nhận số lượng muốn đổi</p>
                                    </div>
                                    <div class="text-center">
                                        <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                            <span class="text-purple-600 text-2xl">3</span>
                                        </div>
                                        <h4 class="font-semibold text-gray-800 mb-2">{{ trans_db('general.cho_duyet') ?? 'Chờ duyệt' }}</h4>
                                        <p class="text-sm text-gray-600">{{ trans_db('nav.admin_se_duyet_va_lien_he_de_trao_qua') ?? 'Admin sẽ duyệt và liên hệ để trao quà' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quà tặng có thể đổi -->
                        <div class="mb-8">
                            <h3 class="text-xl font-bold mb-4 text-gray-800">{{ trans_db('common.qua_tang_co_the_doi') ?? 'Quà tặng có thể đổi' }}</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                @if ($availableGifts->count() > 0)
                                    @foreach ($availableGifts as $gift)
                                        <div class="bg-white border rounded-lg p-4 shadow-sm hover:shadow-md transition">
                                            @if ($gift->image)
                                                <img src="{{ asset('storage/' . $gift->image) }}"
                                                    alt="{{ $gift->name }}"
                                                    class="w-full h-32 object-cover rounded mb-3">
                                            @else
                                                <div class="w-full h-32 bg-gray-200 rounded mb-3 flex items-center justify-center">
                                                    <span class="text-gray-500">{{ trans_db('common.khong_co_anh') ?? 'Không có ảnh' }}</span>
                                                </div>
                                            @endif
                                            <h4 class="font-semibold text-lg mb-2">{{ $gift->name }}</h4>
                                            <p class="text-gray-600 text-sm mb-3">{{ Str::limit($gift->description, 100) }}</p>
                                            <div class="flex justify-between items-center mb-2">
                                                <span class="font-bold text-green-600">{{ number_format($gift->value) }} {{ trans_db('general.diem') ?? 'điểm' }}</span>
                                                <button onclick="redeemGift({{ $gift->id }}, '{{ $gift->name }}', {{ $gift->value }}, {{ $gift->quantity }})"
                                                    class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition">
                                                    {{ trans_db('general.doi_qua') ?? 'Đổi quà' }}
                                                </button>
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                {{ trans_db('general.con_lai') ?? 'Còn lại' }}: {{ $gift->quantity }} {{ trans_db('general.san_pham') ?? 'sản phẩm' }}
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <div class="col-span-3">
                                        <div class="text-center py-8">
                                            <div class="text-gray-400 text-6xl mb-4">🎁</div>
                                            <p class="text-gray-500 text-lg mb-2">{{ trans_db('common.khong_co_qua_tang_nao_co_the_doi') ?? 'Không có quà tặng nào có thể đổi' }}</p>
                                            <p class="text-gray-400">{{ trans_db('action.hay_tich_them_diem_de_doi_qua_nhe') ?? 'Hãy tích thêm điểm để đổi quà nhé!' }}</p>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Lịch sử đổi quà -->
                        <div class="mb-8">
                            <h3 class="text-xl font-bold mb-4 text-gray-800">{{ trans_db('general.lich_su_doi_qua_gan_day') ?? 'Lịch sử đổi quà gần đây' }}</h3>
                            <div class="bg-gray-50 rounded-lg p-4">
                                @if ($redemptionHistory->count() > 0)
                                    <div class="space-y-3">
                                        @foreach ($redemptionHistory as $redemption)
                                            <div class="flex justify-between items-center p-4 bg-white rounded border hover:bg-gray-50 transition">
                                                <div class="flex items-center gap-4">
                                                    <div class="w-12 h-12 bg-gray-200 rounded flex items-center justify-center">
                                                        <span class="text-gray-500 text-lg">🎁</span>
                                                    </div>
                                                    <div>
                                                        <p class="font-medium text-gray-800">{{ $redemption->gift->name ?? trans_db('general.qua_tang') ?? 'Quà tặng' }}</p>
                                                        <p class="text-sm text-gray-600">{{ $redemption->created_at->format('d/m/Y H:i') }}</p>
                                                        <p class="text-sm text-gray-500">{{ trans_db('general.so_luong') ?? 'Số lượng' }}: {{ $redemption->quantity }}</p>
                                                    </div>
                                                </div>
                                                <div class="text-right">
                                                    <span class="font-bold text-red-600">-{{ $redemption->point_used }} {{ trans_db('general.diem') ?? 'điểm' }}</span>
                                                    <div class="mt-1">
                                                        <span class="px-2 py-1 rounded text-xs font-medium
                                                            @if ($redemption->status === 'approved') bg-green-100 text-green-800
                                                            @elseif($redemption->status === 'pending') bg-yellow-100 text-yellow-800
                                                            @elseif($redemption->status === 'completed') bg-blue-100 text-blue-800
                                                            @else bg-red-100 text-red-800 @endif">
                                                            {{ ucfirst($redemption->status) }}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                @else
                                    <div class="text-center py-8">
                                        <div class="text-gray-400 text-4xl mb-4">📋</div>
                                        <p class="text-gray-500">{{ trans_db('common.chua_co_lich_su_doi_qua') ?? 'Chưa có lịch sử đổi quà' }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal đổi quà -->
    <div id="redeemModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-md w-full p-6">
                <h3 class="text-lg font-bold mb-4">{{ trans_db('general.doi_qua_tang') ?? 'Đổi quà tặng' }}</h3>
                <div id="giftInfo" class="mb-4">
                    <p><strong>{{ trans_db('form.ten_qua') ?? 'Tên quà' }}:</strong> <span id="giftName"></span></p>
                    <p><strong>{{ trans_db('general.gia_tri') ?? 'Giá trị' }}:</strong> <span id="giftValue"></span> {{ trans_db('general.diem') ?? 'điểm' }}</p>
                    <p><strong>{{ trans_db('general.so_luong_con_lai') ?? 'Số lượng còn lại' }}:</strong> <span id="giftQuantity"></span></p>
                </div>
                <div class="mb-4">
                    <label for="quantity" class="block text-sm font-medium text-gray-700 mb-2">{{ trans_db('general.so_luong_muon_doi') ?? 'Số lượng muốn đổi' }}</label>
                    <input type="number" id="quantity" min="1" max="10" value="1"
                        class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-green-500">
                </div>
                <div class="mb-4">
                    <p><strong>{{ trans_db('general.tong_diem_can') ?? 'Tổng điểm cần' }}:</strong> <span id="totalPoints"></span></p>
                </div>
                <div class="flex justify-end space-x-3">
                    <button onclick="closeRedeemModal()"
                        class="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50">
                        {{ trans_db('form.huy_bo') ?? 'Hủy bỏ' }}
                    </button>
                    <button onclick="confirmRedeem()"
                        class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                        {{ trans_db('general.xac_nhan_doi_qua') ?? 'Xác nhận đổi quà' }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</x-guest-layout>
