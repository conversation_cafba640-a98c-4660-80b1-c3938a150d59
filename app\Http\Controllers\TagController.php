<?php

namespace App\Http\Controllers;

use App\Models\Tag;
use App\Models\Post;
use Illuminate\Http\Request;

class TagController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $tags = Tag::withCount('posts')->orderBy('name')->paginate(20);
        
        return view('tags.index', compact('tags'));
    }

    /**
     * Display the specified resource.
     */
    public function show($locale, Tag $tag)
    {
        $posts = Post::whereHas('tags', function($query) use ($tag) {
            $query->where('tags.id', $tag->id);
        })
        ->with(['category', 'tags'])
        ->where('published_at', '<=', now())
        ->orderBy('published_at', 'desc')
        ->paginate(12);

        return view('tags.show', compact('tag', 'posts'));
    }
}
