<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PointHistory;

class ApiPointController extends Controller
{
    /**
     * @OA\Post(
     *     path="/api/app/points/history-points-add",
     *     tags={"Points"},
     *     summary="Lịch sử cộng điểm",
     *     description="Lấy lịch sử các lần cộng điểm của người dùng (yêu cầu đăng nhập, có phân trang)",
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Số trang",
     *         required=false,
     *         @OA\Schema(type="integer", default=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Số bản ghi mỗi trang (tối đa 50)",
     *         required=false,
     *         @OA\Schema(type="integer", default=15, maximum=50)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="array",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="change", type="integer", example=5),
     *                     @OA\Property(property="description", type="string", example="Cộng điểm khi đăng nhập"),
     *                     @OA\Property(property="created_at", type="string", example="2024-12-20 10:30:45")
     *                 )
     *             ),
     *             @OA\Property(property="pagination", type="object",
     *                 @OA\Property(property="current_page", type="integer", example=1),
     *                 @OA\Property(property="per_page", type="integer", example=15),
     *                 @OA\Property(property="total", type="integer", example=42),
     *                 @OA\Property(property="last_page", type="integer", example=3)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Chưa đăng nhập",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Chưa đăng nhập")
     *         )
     *     )
     * )
     */
    public function historyPointsAdd(Request $request)
    {
        $user = $request->user();
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Chưa đăng nhập'
            ], 401);
        }
        $histories = PointHistory::where('account_id', $user->id)
            ->where('change', '>', 0)
            ->orderBy('created_at', 'desc')
            ->paginate(15);
        $data = $histories->map(function($item) {
            return [
                'id' => $item->id,
                'change' => $item->change,
                'description' => $item->description,
                'created_at' => $item->created_at ? formatDateTime($item->created_at) : null,
            ];
        });
        return response()->json([
            'success' => true,
            'data' => $data,
            'pagination' => [
                'current_page' => $histories->currentPage(),
                'per_page' => $histories->perPage(),
                'total' => $histories->total(),
                'last_page' => $histories->lastPage(),
            ]
        ]);
    }
} 