<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Category;
use App\Models\Post;
use App\Models\Tag;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
        User::factory()->create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('DcRGlUxitcNt5uqK')
        ]);

        // Tạo mẫu 5 tài khoản account
        \App\Models\Account::factory(5)->create();

        // Tạo mẫu 10 điểm đổ rác tại TP.HCM (tất cả đều approved)
        \App\Models\Report::factory(10)->create();

        $this->call([
            EventSeeder::class,         // Sử dụng EventSeeder để tạo sự kiện thực tế
            GiftSeeder::class,
            LanguageSeeder::class,
            TranslationSeeder::class,
            EventTranslationSeeder::class,  // Thêm translation cho sự kiện
        ]);
    }
}
