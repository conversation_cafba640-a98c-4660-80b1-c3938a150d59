<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- SEO Meta -->
    <meta name="robots" content="noindex, nofollow">
    <meta name="description" content="{{ trans_db('action.ecosolves_nen_tang_cong_dong_kien_tao_giai_phap_mo') }}">
    <meta name="keywords" content="{{ trans_db('general.moi_truong_cong_dong_giai_phap_ben_vung_ecosolves_') }}">
    <meta name="author" content="EcoSolves Team">
    <meta name="theme-color" content="#10B981">
    <!-- Open Graph -->
    <meta property="og:title" content="{{ trans_db('general.ecosolves_nen_tang_cong_dong_moi_truong') }}">
    <meta property="og:description"
        content="{{ trans_db('action.tham_gia_cong_dong_ecosolves_de_cung_kien_tao_giai') }}">
    <meta property="og:type" content="website">
    <meta property="og:image" content="/images/logo.png">
    <meta property="og:url" content="{{ url()->current() }}">

    <title>{{ trans_db('general.ecosolves_nen_tang_cong_dong_moi_truong') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" />
    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

<body class="font-sans text-gray-900 antialiased">
    {{ $slot }}
    {{-- Jquery cdn --}}
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script>
        // Setup CSRF token for all AJAX requests
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        // Spinner SVG
        $(function() {
            // Bước 1: Gửi email lấy mã xác thực
            $('#register-step-email').on('submit', function(e) {
                e.preventDefault();
                var $btn = $(this).find('button[type=submit]');
                $btn.addClass('opacity-50 pointer-events-none');
                var email = $('#reg_email').val();
                $.ajax({
                    url: '/api/register/email',
                    method: 'POST',
                    data: {
                        email: email,
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(res) {
                        if (res.success) {
                            $('#register-step-email').fadeOut(200, function() {
                                $('#register-step-code').fadeIn(200);
                            });
                            toastr.success('{{ trans_db('form.ma_xac_thuc_da_duoc_gui_ve_email') }}');
                        } else {
                            toastr.error(res.message || '{{ trans_db('status.co_loi_xay_ra') }}');
                        }
                    },
                    error: function(xhr) {
                        toastr.error(xhr.responseJSON?.message || '{{ trans_db('status.co_loi_xay_ra') }}');
                    },
                    complete: function() {
                        $btn.removeClass('opacity-50 pointer-events-none');
                        $btn.find('svg').parent().remove();
                    }
                });
            });
            // Bước 2: Kiểm tra mã xác thực
            $('#register-step-code').on('submit', function(e) {
                e.preventDefault();
                var $btn = $(this).find('button[type=submit]');
                $btn.addClass('opacity-50 pointer-events-none');
                var code = $('#reg_code').val();
                var email = $('#reg_email').val();
                $.ajax({
                    url: '/api/register/verify',
                    method: 'POST',
                    data: {
                        code: code,
                        email: email,
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(res) {
                        if (res.success) {
                            $('#register-step-code').fadeOut(200, function() {
                                $('#register-step-password').fadeIn(200);
                            });
                            toastr.success('{{ trans_db('status.xac_thuc_thanh_cong') }}');
                        } else {
                            toastr.error(res.message || '{{ trans_db('general.ma_xac_thuc_khong_dung') }}');
                        }
                    },
                    error: function(xhr) {
                        toastr.error(xhr.responseJSON?.message || '{{ trans_db('status.co_loi_xay_ra') }}');
                    },
                    complete: function() {
                        $btn.removeClass('opacity-50 pointer-events-none');
                        $btn.find('svg').parent().remove();
                    }
                });
            });
            // Bước 3: Đặt mật khẩu mới
            $('#register-step-password').on('submit', function(e) {
                e.preventDefault();
                var $btn = $(this).find('button[type=submit]');
                $btn.addClass('opacity-50 pointer-events-none');
                var email = $('#reg_email').val();
                var username = $('#reg_username').val();
                var full_name = $('#reg_full_name').val();
                var password = $('#reg_password').val();
                var password_confirmation = $('#reg_password_confirmation').val();
                $.ajax({
                    url: '/api/register/complete',
                    method: 'POST',
                    xhrFields: {
                        withCredentials: true
                    },
                    data: {
                        email: email,
                        username: username,
                        full_name: full_name,
                        password: password,
                        password_confirmation: password_confirmation,
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(res) {
                        if (res.success) {
                            $('#register-step-password').fadeOut(200);
                            toastr.success('{{ trans_db('form.dang_ky_thanh_cong_dang_chuyen_huong') }}');
                            setTimeout(function() {
                                window.location.href = res.redirect || '/dashboard';
                            }, 1500);
                        } else {
                            toastr.error(res.message || '{{ trans_db('status.co_loi_xay_ra') }}');
                        }
                    },
                    error: function(xhr) {
                        toastr.error(xhr.responseJSON?.message || '{{ trans_db('status.co_loi_xay_ra') }}');
                    },
                    complete: function() {
                        $btn.removeClass('opacity-50 pointer-events-none');
                        $btn.find('svg').parent().remove();
                    }
                });
            });
            // Đăng nhập form submit truyền thống - không cần AJAX
            // Form login sẽ submit bình thường đến route login.post

            // Preview avatar nếu có input avatar
            if ($('#avatar').length) {
                $('#avatar').on('change', function(e) {
                    const [file] = this.files;
                    if (file) {
                        $('#avatar-preview').attr('src', URL.createObjectURL(file));
                    }
                });
            }

            // Cập nhật tài khoản AJAX nếu có form
            if ($('#profile-update-form').length) {
                $('#profile-update-form').on('submit', function(e) {
                    e.preventDefault();
                    var formData = new FormData(this);
                    var $btn = $(this).find('button[type=submit]');
                    $btn.addClass('opacity-50 pointer-events-none');
                    $.get('/sanctum/csrf-cookie').then(function() {
                        $.ajax({
                            url: '/api/profile/update',
                            method: 'POST',
                            data: formData,
                            processData: false,
                            contentType: false,
                            xhrFields: {
                                withCredentials: true
                            },
                            success: function(res) {
                                toastr.success(res.message || '{{ trans_db('status.cap_nhat_thanh_cong') }}');
                                if (res.avatar_url) {
                                    $('#avatar-preview').attr('src', res.avatar_url);
                                }
                                window.location.href = '/{{ app()->getLocale() }}/dashboard/profile';
                            },
                            error: function(xhr) {
                                let msg = "{{ trans_db('status.co_loi_xay_ra') }}";
                                if (xhr.status === 422) {
                                    let errors = xhr.responseJSON.errors;
                                    msg = Object.values(errors).map(arr => arr.join(
                                        '<br>')).join('<br>');
                                } else if (xhr.responseJSON?.message) {
                                    msg = xhr.responseJSON.message;
                                }
                                toastr.error(msg);
                            },
                            complete: function() {
                                $btn.removeClass('opacity-50 pointer-events-none');
                            }
                        });
                    });
                });
            }

            // Logout form submit truyền thống - không cần AJAX
            // Form logout sẽ submit bình thường đến route logout
        });
    </script>
    <script>
        let currentGiftId = null;
        let currentGiftValue = 0;

        function redeemGift(giftId, giftName, giftValue, giftQuantity) {
            currentGiftId = giftId;
            currentGiftValue = giftValue;
            
            $('#giftName').text(giftName);
            $('#giftValue').text(giftValue.toLocaleString());
            $('#giftQuantity').text(giftQuantity);
            $('#quantity').attr('max', Math.min(giftQuantity, 10)).val(1);
            
            updateTotalPoints();
            $('#redeemModal').removeClass('hidden');
        }

        function closeRedeemModal() {
            $('#redeemModal').addClass('hidden');
        }

        function updateTotalPoints() {
            const quantity = parseInt($('#quantity').val());
            const total = quantity * currentGiftValue;
            $('#totalPoints').text(total.toLocaleString());
        }

        function confirmRedeem() {
            const quantity = parseInt($('#quantity').val());
            
            if (quantity < 1 || quantity > 10) {
                toastr.error('{{ trans_db('general.so_luong_phai_tu_1_den_10') }}');
                return;
            }

            // Disable button
            const $confirmBtn = $('#redeemModal button:last-child');
            $confirmBtn.prop('disabled', true).text('{{ trans_db('status.dang_xu_ly') }}');

            $.ajax({
                url: '/api/gifts/redeem',
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                data: JSON.stringify({
                    gift_id: currentGiftId,
                    quantity: quantity
                }),
                success: function(data) {
                    if (data.success) {
                        toastr.success(data.message || '{{ trans_db('status.doi_qua_thanh_cong') }}');
                        closeRedeemModal();
                        // Reload page to update data
                        setTimeout(function() {
                            window.location.reload();
                        }, 1500);
                    } else {
                        toastr.error(data.message || '{{ trans_db('general.doi_qua_that_bai') }}');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error:', error);
                    toastr.error('{{ trans_db('status.co_loi_xay_ra_vui_long_thu_lai') }}');
                },
                complete: function() {
                    $confirmBtn.prop('disabled', false).text('{{ trans_db('general.xac_nhan_doi_qua') }}');
                }
            });
        }

        // Update total points when quantity changes
        $(document).ready(function() {
            $('#quantity').on('input', updateTotalPoints);
        });
    </script>
</body>

</html>
