<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Report>
 */
class ReportFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // Lấy random account id nếu có
        $accountId = \App\Models\Account::inRandomOrder()->first()?->id ?? 1;

        // Tọa độ các điểm đổ rác thực tế tại TP.HCM
        $hcmTrashLocations = [
            ['lat' => 10.7769, 'lng' => 106.7009, 'desc' => 'Điểm đổ rác gần chợ Bến Thành, quận 1'],
            ['lat' => 10.7625, 'lng' => 106.6820, 'desc' => '<PERSON>hu vực đổ rác ven kênh <PERSON>, quận 3'],
            ['lat' => 10.8142, 'lng' => 106.6438, 'desc' => '<PERSON><PERSON>ể<PERSON> tập kết rác tại quận Tân Bình'],
            ['lat' => 10.7308, 'lng' => 106.7175, 'desc' => 'Khu đổ rác gần cầu Sài Gòn, quận 4'],
            ['lat' => 10.7546, 'lng' => 106.6677, 'desc' => 'Điểm rác thải ven sông Sài Gòn, quận 5'],
            ['lat' => 10.7429, 'lng' => 106.6509, 'desc' => 'Khu vực đổ rác tại quận 6'],
            ['lat' => 10.7335, 'lng' => 106.6181, 'desc' => 'Điểm tập trung rác thải quận 8'],
            ['lat' => 10.8693, 'lng' => 106.8037, 'desc' => 'Khu đổ rác tại quận 9 (Thủ Đức)'],
            ['lat' => 10.8506, 'lng' => 106.7717, 'desc' => 'Điểm rác thải khu công nghiệp Thủ Đức'],
            ['lat' => 10.7215, 'lng' => 106.6291, 'desc' => 'Khu vực đổ rác ven kênh quận 7'],
        ];

        // Chọn ngẫu nhiên một điểm từ danh sách
        $location = $this->faker->randomElement($hcmTrashLocations);

        // Thêm một chút biến động nhỏ cho tọa độ để tạo sự đa dạng
        $lat = $location['lat'] + $this->faker->randomFloat(4, -0.005, 0.005);
        $lng = $location['lng'] + $this->faker->randomFloat(4, -0.005, 0.005);

        return [
            'created_by' => $accountId,
            'image' => 'reports/images/' . $this->faker->uuid . '.jpg',
            'description' => $location['desc'] . ' - ' . $this->faker->randomElement([
                'Rác thải sinh hoạt tập trung nhiều',
                'Khu vực có nhiều túi rác bỏ bừa bãi',
                'Điểm đổ rác không đúng quy định',
                'Rác thải công nghiệp cần xử lý',
                'Khu vực ô nhiễm môi trường nghiêm trọng',
                'Điểm tập kết rác cần dọn dẹp gấp',
                'Rác thải nhựa và túi nilon nhiều',
                'Khu vực có mùi hôi thối từ rác thải'
            ]),
            'latitude' => $lat,
            'longitude' => $lng,
            'gmap_link' => "https://maps.google.com/?q={$lat},{$lng}",
            'status' => 'approved', // Tất cả đều được approved
        ];
    }
}
