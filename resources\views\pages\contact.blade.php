@extends('layouts.app')

@section('title('nav.lien_he') }}')

@section('header('nav.lien_he_voi_chung_toi') }}')

@section('content')
    <div class="max-w-3xl mx-auto-8">
            <p class="text-gray-600 mb-4">{{ trans_db('nav.chung_toi_luon_san_sang_lang_nghe_y_kien_va_giai_d') }}
            <div>
                <h3 class="text-lg font-semibold mb-4">{{ trans_db('form.gui_tin_nhan_cho_chung_toi') }}
                
                @if(session('success'))
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">{{ session('success') }}</span>
                    </div>
                @endif

                <form action="{{ route('contact.submit') }}" method="POST-4">
                    @csrf
                    <div>
                        <label for="name-1">{{ trans_db('form.ho_va_tenlabel_input_type') }}
                        @error('name')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="email-1">{{ trans_db('form.email') }}
                        <input type="email-500" required>
                        @error('email')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="subject-1">{{ trans_db('general.tieu_delabel_input_type') }}
                        @error('subject')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="message-1">{{ trans_db('general.noi_dung') }}
                        <textarea name="message" rows="5" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" required></textarea>
                        @error('message')
                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <button type="submit-150">{{ trans_db('nav.thong_tin_lien_heh3_div_class') }}
                    <div class="flex items-start-1">
                            <svg class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z-3">
                            <h4 class="text-md font-medium text-gray-900">{{ trans_db('general.123_duong_abc_quan_xyz_thanh_pho_hcm_viet_namp_div') }}
                        <div class="flex-shrink-0 mt-1">
                            <svg class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z-3">
                            <h4 class="text-md font-medium text-gray-900">{{ trans_db('general.dien_thoaih4_p_class') }}(+84) 123 456 789</p>
                        </div>
                    </div>

                    <div class="flex items-start-1">
                            <svg class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z-3">
                            <h4 class="text-md font-medium text-gray-900">{{ trans_db('form.email') }}
                            <p class="text-gray-600"><EMAIL></p>
                        </div>
                    </div>

                    <div class="flex items-start-1">
                            <svg class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z-3">
                            <h4 class="text-md font-medium text-gray-900">{{ trans_db('general.chu_nhat_nghip_div_div_div_div_class') }}
                    <h4 class="text-md font-medium text-gray-900 mb-2">{{ trans_db('general.ket_noi_voi_chung_toih4_div_class') }}
                        <a href="#" class="text-gray-400 hover:text-indigo-600">
                            <span class="sr-only-6" fill="currentColor" viewBox="0 0 24 24">
                                <path fill-rule="evenodd"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-indigo-600">
                            <span class="sr-only-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-indigo-600">
                            <span class="sr-only-6" fill="currentColor" viewBox="0 0 24 24">
                                <path fill-rule="evenodd"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-indigo-600">
                            <span class="sr-only-6" fill="currentColor" viewBox="0 0 24 24">
                                <path fill-rule="evenodd-8">
            <h3 class="text-lg font-semibold mb-4">{{ trans_db('general.thay_the_bang_iframe_google_maps_thuc_te_div_class') }}
                    <p class="text-gray-500">{{ trans_db('general.ban_do_google_maps_se_duoc_hien_thi_o_day') }}
                </div>
            </div>
        </div>
    </div>
@endsection