<x-guest-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ trans_db('general.su_kien_cua_toi') ?? 'Sự kiện của tôi' }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="flex min-h-[400px]">
                    <!-- Sidebar -->
                    @include('dashboard.sidebar')

                    <!-- Main Content -->
                    <div class="w-3/4 p-6">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-2xl font-bold text-gray-800">{{ trans_db('general.danh_sach_su_kien') ?? 'Danh sách sự kiện' }}</h3>
                            <a href="{{ route('dashboard.events.create', ['locale' => app()->getLocale()]) }}" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">{{ trans_db('action.tao_su_kien_moi') ?? 'Tạo sự kiện mới' }}</a>
                        </div>

                        <!-- Sự kiện do bạn tạo -->
                        <div class="mb-10">
                            <h4 class="text-lg font-semibold mb-3 text-gray-700">{{ trans_db('action.su_kien_do_ban_tao') ?? 'Sự kiện do bạn tạo' }}</h4>
                            @if($createdEvents->count())
                                <div class="overflow-x-auto">
                                    <table class="min-w-full bg-white border rounded-lg">
                                        <thead>
                                            <tr>
                                                <th class="px-4 py-2 border">{{ trans_db('form.ten_su_kien') ?? 'Tên sự kiện' }}</th>
                                                <th class="px-4 py-2 border">{{ trans_db('general.thoi_gian') ?? 'Thời gian' }}</th>
                                                <th class="px-4 py-2 border">{{ trans_db('general.dia_diem') ?? 'Địa điểm' }}</th>
                                                <th class="px-4 py-2 border">{{ trans_db('general.trang_thai') ?? 'Trạng thái' }}</th>
                                                <th class="px-4 py-2 border">{{ trans_db('action.ngay_tao') ?? 'Ngày tạo' }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($createdEvents as $event)
                                                <tr>
                                                    <td class="px-4 py-2 border font-semibold text-blue-700">
                                                        <a href="{{ localized_route('event.show', ['slug' => $event->slug]) }}" target="_blank">{{ $event->title }}</a>
                                                    </td>
                                                    <td class="px-4 py-2 border">{{ $event->start_date ? $event->start_date->format('d/m/Y H:i') : '-' }}</td>
                                                    <td class="px-4 py-2 border">{{ $event->location }}</td>
                                                    <td class="px-4 py-2 border">
                                                        @if($event->status === 'pending')
                                                            <span class="text-yellow-600 font-semibold">{{ trans_db('general.cho_duyet') ?? 'Chờ duyệt' }}</span>
                                                        @elseif($event->status === 'approved')
                                                            <span class="text-green-600 font-semibold">{{ trans_db('general.da_duyet') ?? 'Đã duyệt' }}</span>
                                                        @elseif($event->status === 'rejected')
                                                            <span class="text-red-600 font-semibold">{{ trans_db('general.tu_choi') ?? 'Từ chối' }}</span>
                                                        @else
                                                            <span class="text-gray-600">{{ ucfirst($event->status) }}</span>
                                                        @endif
                                                    </td>
                                                    <td class="px-4 py-2 border">{{ $event->created_at->format('d/m/Y H:i') }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <p class="text-gray-500">{{ trans_db('action.ban_chua_tao_su_kien_nao') ?? 'Bạn chưa tạo sự kiện nào.' }}</p>
                            @endif
                        </div>

                        <!-- Sự kiện bạn đã tham gia -->
                        <div>
                            <h4 class="text-lg font-semibold mb-3 text-gray-700">{{ trans_db('general.su_kien_ban_da_tham_gia') ?? 'Sự kiện bạn đã tham gia' }}</h4>
                            @if($joinedEvents->count())
                                <div class="overflow-x-auto">
                                    <table class="min-w-full bg-white border rounded-lg">
                                        <thead>
                                            <tr>
                                                <th class="px-4 py-2 border">{{ trans_db('form.ten_su_kien') ?? 'Tên sự kiện' }}</th>
                                                <th class="px-4 py-2 border">{{ trans_db('general.thoi_gian') ?? 'Thời gian' }}</th>
                                                <th class="px-4 py-2 border">{{ trans_db('general.dia_diem') ?? 'Địa điểm' }}</th>
                                                <th class="px-4 py-2 border">{{ trans_db('general.trang_thai') ?? 'Trạng thái' }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($joinedEvents as $event)
                                                <tr>
                                                    <td class="px-4 py-2 border font-semibold text-blue-700">
                                                        <a href="{{ localized_route('event.show', ['slug' => $event->slug]) }}" target="_blank">{{ $event->title }}</a>
                                                    </td>
                                                    <td class="px-4 py-2 border">{{ $event->start_date ? $event->start_date->format('d/m/Y H:i') : '-' }}</td>
                                                    <td class="px-4 py-2 border">{{ $event->location }}</td>
                                                    <td class="px-4 py-2 border">
                                                        @if($event->status === 'pending')
                                                            <span class="text-yellow-600 font-semibold">{{ trans_db('general.cho_duyet') ?? 'Chờ duyệt' }}</span>
                                                        @elseif($event->status === 'approved')
                                                            <span class="text-green-600 font-semibold">{{ trans_db('general.da_duyet') ?? 'Đã duyệt' }}</span>
                                                        @elseif($event->status === 'rejected')
                                                            <span class="text-red-600 font-semibold">{{ trans_db('general.tu_choi') ?? 'Từ chối' }}</span>
                                                        @else
                                                            <span class="text-gray-600">{{ ucfirst($event->status) }}</span>
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <p class="text-gray-500">{{ trans_db('general.ban_chua_tham_gia_su_kien_nao') ?? 'Bạn chưa tham gia sự kiện nào.' }}</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-guest-layout>