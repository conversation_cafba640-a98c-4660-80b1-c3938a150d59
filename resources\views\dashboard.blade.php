<x-guest-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ trans_db('nav.dashboard') ?? 'Dashboard' }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="flex min-h-[400px]">
                    <!-- Sidebar -->
                    @include('dashboard.sidebar')

                    <!-- Main Content -->
                    <div class="w-3/4 p-6">
                        @if(session('success'))
                            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                                {{ session('success') }}
                            </div>
                        @endif

                        @if(session('error'))
                            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                                {{ session('error') }}
                            </div>
                        @endif

                        <!-- Welcome Section -->
                        <div class="mb-8">
                            <div class="bg-gradient-to-r from-[#10B981] to-[#0EA5E9] rounded-lg p-6 text-white">
                                <h3 class="text-2xl font-bold mb-2">{{ trans_db('general.chao_mung_ban_tro_lai') }}</h3>
                                <p class="text-lg opacity-90">{{ $personalInfo['full_name'] }}</p>
                                <p class="opacity-80">{{ trans_db('common.ban_hien_co') }} <span class="font-bold text-xl">{{ number_format($personalInfo['point']) }} {{ trans_db('general.diem') }}</span> {{ trans_db('general.de_doi_qua') }}</p>
                            </div>
                        </div>

                        <!-- Quick Stats -->
                        <div class="mb-8">
                            <h3 class="text-xl font-bold mb-4 text-gray-800">{{ trans_db('general.thong_ke_nhanh') }}</h3>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div class="bg-blue-50 p-4 rounded-lg border">
                                    <div class="text-2xl font-bold text-blue-600">{{ $personalStats['total_reports'] }}</div>
                                    <div class="text-sm text-gray-600">{{ trans_db('general.tong_bao_cao') }}</div>
                                </div>
                                <div class="bg-green-50 p-4 rounded-lg border">
                                    <div class="text-2xl font-bold text-green-600">{{ $personalStats['approved_reports'] }}</div>
                                    <div class="text-sm text-gray-600">{{ trans_db('general.bao_cao_da_duyet') }}</div>
                                </div>
                                <div class="bg-yellow-50 p-4 rounded-lg border">
                                    <div class="text-2xl font-bold text-yellow-600">{{ $personalStats['total_redemptions'] }}</div>
                                    <div class="text-sm text-gray-600">{{ trans_db('general.lan_doi_qua') }}</div>
                                </div>
                                <div class="bg-purple-50 p-4 rounded-lg border">
                                    <div class="text-2xl font-bold text-purple-600">{{ number_format($personalStats['total_points_earned']) }}</div>
                                    <div class="text-sm text-gray-600">{{ trans_db('general.diem_da_tich') }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="mb-8">
                            <h3 class="text-xl font-bold mb-4 text-gray-800">{{ trans_db('general.thao_tac_nhanh') }}</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <a href="{{ route('dashboard.profile', ['locale' => app()->getLocale()]) }}" class="bg-white border rounded-lg p-4 hover:shadow-md transition text-center">
                                    <div class="text-3xl mb-2">👤</div>
                                    <h4 class="font-semibold text-gray-800">{{ trans_db('action.xem_thong_tin_ca_nhan') }}</h4>
                                    <p class="text-sm text-gray-600">{{ trans_db('action.cap_nhat_va_xem_thong_tin_tai_khoan') }}</p>
                                </a>
                                <a href="{{ route('dashboard.statistics', ['locale' => app()->getLocale()]) }}" class="bg-white border rounded-lg p-4 hover:shadow-md transition text-center">
                                    <div class="text-3xl mb-2">📊</div>
                                    <h4 class="font-semibold text-gray-800">{{ trans_db('action.xem_thong_ke_chi_tiet') }}</h4>
                                    <p class="text-sm text-gray-600">{{ trans_db('common.thong_ke_hoat_dong_va_lich_su') }}</p>
                                </a>
                                <a href="{{ route('dashboard.gifts', ['locale' => app()->getLocale()]) }}" class="bg-white border rounded-lg p-4 hover:shadow-md transition text-center">
                                    <div class="text-3xl mb-2">🎁</div>
                                    <h4 class="font-semibold text-gray-800">{{ trans_db('general.doi_qua_tang') }}</h4>
                                    <p class="text-sm text-gray-600">{{ trans_db('action.xem_va_doi_qua_voi_diem_tich_luy') }}</p>
                                </a>
                            </div>
                        </div>

                        <!-- Recent Activities -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Recent Point History -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="font-bold text-lg mb-4 text-gray-800">{{ trans_db('general.lich_su_tich_diem_gan_day') }}</h4>
                                @if($pointHistory->count() > 0)
                                    <div class="space-y-3">
                                        @foreach($pointHistory->take(5) as $history)
                                            <div class="flex justify-between items-center p-3 bg-white rounded border">
                                                <div>
                                                    <p class="font-medium text-sm">{{ Str::limit($history->description, 50) }}</p>
                                                    <p class="text-xs text-gray-600">{{ $history->created_at->format('d/m/Y H:i') }}</p>
                                                </div>
                                                <div class="text-right">
                                                    <span class="font-bold text-sm {{ $history->change > 0 ? 'text-green-600' : 'text-red-600' }}">
                                                        {{ $history->change > 0 ? '+' : '' }}{{ $history->change }} điểm
                                                    </span>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                    <div class="text-center mt-4">
                                        <a href="{{ route('dashboard.statistics') }}" class="text-[#10B981] hover:underline text-sm">{{ trans_db('action.xem_tat_ca') }}</a>
                                    </div>
                                @else
                                    <p class="text-gray-500 text-center py-4">{{ trans_db('common.chua_co_lich_su_tich_diem') }}</p>
                                @endif
                            </div>

                            <!-- Recent Gift Redemptions -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="font-bold text-lg mb-4 text-gray-800">{{ trans_db('general.lich_su_doi_qua_gan_day') }}</h4>
                                @if($redemptionHistory->count() > 0)
                                    <div class="space-y-3">
                                        @foreach($redemptionHistory->take(5) as $redemption)
                                            <div class="flex justify-between items-center p-3 bg-white rounded border">
                                                <div>
                                                    <p class="font-medium text-sm">{{ $redemption->gift->name ?? trans_db('general.qua_tang') }}</p>
                                                    <p class="text-xs text-gray-600">{{ $redemption->created_at->format('d/m/Y H:i') }}</p>
                                                </div>
                                                <div class="text-right">
                                                    <span class="font-bold text-sm text-red-600">-{{ $redemption->point_used }} điểm</span>
                                                    <div class="text-xs text-gray-600">{{ ucfirst($redemption->status) }}</div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                    <div class="text-center mt-4">
                                        <a href="{{ route('dashboard.gifts', ['locale' => app()->getLocale()]) }}" class="text-[#10B981] hover:underline text-sm">{{ trans_db('action.xem_tat_ca') }}</a>
                                    </div>
                                @else
                                    <p class="text-gray-500 text-center py-4">{{ trans_db('common.chua_co_lich_su_doi_qua') }}</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal đổi quà -->
    <div id="redeemModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-md w-full p-6">
                <h3 class="text-lg font-bold mb-4">{{ trans_db('general.doi_qua_tang') }}</h3>
                <div id="giftInfo" class="mb-4">
                    <p><strong>{{ trans_db('form.ten_qua') }}:</strong> <span id="giftName"></span></p>
                    <p><strong>{{ trans_db('general.gia_tri') }}:</strong> <span id="giftValue"></span> {{ trans_db('general.diem') }}</p>
                    <p><strong>{{ trans_db('general.so_luong_con_lai') }}:</strong> <span id="giftQuantity"></span></p>
                </div>
                <div class="mb-4">
                    <label for="quantity" class="block text-sm font-medium text-gray-700 mb-2">{{ trans_db('general.so_luong_muon_doi') }}</label>
                    <input type="number" id="quantity" min="1" max="10" value="1" 
                           class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:border-green-500">
                </div>
                <div class="mb-4">
                    <p><strong>{{ trans_db('general.tong_diem_can') }}:</strong> <span id="totalPoints"></span></p>
                </div>
                <div class="flex justify-end space-x-3">
                    <button onclick="closeRedeemModal()"
                            class="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50">{{ trans_db('form.huy_bo') }}</button>
                    <button onclick="confirmRedeem()"
                            class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                        Xác nhận đổi quà
                    </button>
                </div>
            </div>
        </div>
    </div>
</x-guest-layout>
