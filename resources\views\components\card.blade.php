@props([
    'title' => null,
    'imageAlt' => '',
    'footer' => null,
    'hoverEffect' => true
])

<div {{ $attributes->merge(['class ' . ($hoverEffect ? 'hover:shadow-md transition-shadow' : '')]) }}>
    @if($image)
        <div class="relative }}" alt="{{ $imageAlt }}" class="w-full h-48 object-cover">
            @if($href)
                <a href="{{ $href }}" class="absolute inset-0"></a>
            @endif
        </div>
    @endif
    
    <div class="p-4">
        @if($title)
            <h3 class="text-lg font-semibold mb-2">
                @if($href)
                    <a href="{{ $href }}" class="text-indigo-600 hover:text-indigo-800">{{ $title }}</a>
                @else
                    <span class="text-gray-900">{{ $title }}</span>
                @endif
            </h3>
        @endif
        
        <div class="text-gray-600">
            {{ $slot }}
        </div>
        
        @if($footer)
            <div class="mt-4 pt-3 border-t border-gray-100">
                {{ $footer }}
            </div>
        @endif
    </div>
</div>