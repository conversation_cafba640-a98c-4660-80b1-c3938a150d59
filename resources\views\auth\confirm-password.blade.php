<x-guest-layout>
    <div class="w-full min-h-screen flex flex-col justify-center items-center bg-gradient-to-br from-[#0EA5E9]/10 via-[#10B981]/10 to-[#4ADE80]/10 py-12 px-4">
        <div class="w-full max-w-md bg-white rounded-2xl shadow-2xl p-8 md:p-10">
            <h2 class="text-3xl font-bold text-center mb-8 bg-gradient-to-r from-[#10B981] to-[#0EA5E9] bg-clip-text text-transparent">{{ trans_db('form.confirm_password') }}</h2>
            <div class="mb-4 text-sm text-gray-600 text-center">
                {{ trans_db('form.day_la_khu_vuc_bao_mat_vui_long_xac_nhan_mat_khau_') }}
            </div>
            <form method="POST" action="{{ route('password.confirm', ['locale' => app()->getLocale()]) }}" class="space-y-6">
                @csrf
                <!-- Password -->
                <div>
                    <x-input-label for="password" :value="{{ trans_db('form.mat_khau') }}" class="text-[#10B981] font-semibold" />
                    <x-text-input id="password" class="block mt-1 w-full rounded-lg border border-[#10B981]/30 focus:border-[#10B981] focus:ring-[#10B981]" type="password" name="password" required autocomplete="current-password" placeholder="{{ trans_db('form.nhap_mat_khau') }}" />
                    <x-input-error :messages="$errors->get('password')" class="mt-2" />
                </div>
                <div class="flex justify-end mt-4">
                    <button type="submit" class="w-full py-3 rounded-lg text-white font-semibold bg-gradient-to-r from-[#10B981] to-[#0EA5E9] hover:from-[#0EA5E9] hover:to-[#10B981] transition">{{ trans_db('general.xac_nhan') }}</button>
                </div>
            </form>
        </div>
    </div>
</x-guest-layout>

