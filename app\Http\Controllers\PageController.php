<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Report;

class PageController extends Controller
{
    public function __construct()
    {
        
    }

    public function report() {
        return view('pages.report');
    }

    public function map() {
        $reports = Report::select('id','image','description','latitude','longitude','gmap_link','status','created_at')
                         ->where('status', '!=', 'rejected')
                         ->orderByDesc('created_at')
                         ->get();
        return view('pages.map', compact('reports'));
    }
    
    /**
     * Display the about page.
     *
     * @return \Illuminate\Http\Response
     */
    public function about()
    {
        return view('pages.about');
    }

    /**
     * Display the contact page.
     *
     * @return \Illuminate\Http\Response
     */
    public function contact()
    {
        return view('pages.contact');
    }

    /**
     * Process the contact form submission.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function submitContact(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        // Here you would typically send an email or store the contact request
        // For now, we'll just redirect with a success message
        
        return redirect()->back()->with('success', 'Thank you for your message. We will get back to you soon!');
    }

    /**
     * Handle report submission
     */
    public function submitReport(Request $request) {
        $request->validate([
            'image' => 'required|image|max:4096',
            'description' => 'required|string|max:1000',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'gmap_link' => 'nullable|url'
        ]);

        // Handle image upload
        $imagePath = $request->file('image')->store('reports', 'public');

        // Create report
        Report::create([
            'created_by' => auth('account')->id(),
            'image' => $imagePath,
            'description' => $request->description,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'gmap_link' => $request->gmap_link,
            'status' => 'pending'
        ]);

        return redirect()->route('pages.map', ['locale' => app()->getLocale()])
            ->with('success', trans_db('status.bao_cao_da_gui_thanh_cong') ?? 'Báo cáo đã được gửi thành công!');
    }
}