<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Thread;
use App\Models\Reply;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class ApiForumController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/app/forum",
     *     tags={"Forum"},
     *     summary="Lấy danh sách threads",
     *     description="Lấy danh sách threads với phân trang và tìm kiếm",
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Số trang",
     *         required=false,
     *         @OA\Schema(type="integer", default=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Số bài viết mỗi trang (tối đa 50)",
     *         required=false,
     *         @OA\Schema(type="integer", default=15, maximum=50)
     *     ),
     *     @OA\Parameter(
     *         name="search",
     *         in="query",
     *         description="Từ khóa tìm kiếm",
     *         required=false,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="sort_by",
     *         in="query",
     *         description="Sắp xếp theo",
     *         required=false,
     *         @OA\Schema(type="string", enum={"created_at", "updated_at", "views", "title"}, default="created_at")
     *     ),
     *     @OA\Parameter(
     *         name="sort_order",
     *         in="query",
     *         description="Thứ tự sắp xếp",
     *         required=false,
     *         @OA\Schema(type="string", enum={"asc", "desc"}, default="desc")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="threads", type="array",
     *                     @OA\Items(
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="title", type="string", example="Thảo luận về môi trường"),
     *                         @OA\Property(property="slug", type="string", example="thao-luan-ve-moi-truong"),
     *                         @OA\Property(property="content", type="string", example="Nội dung bài viết..."),
     *                         @OA\Property(property="views", type="integer", example=150),
     *                         @OA\Property(property="replies_count", type="integer", example=5),
     *                         @OA\Property(property="created_at", type="string", example="2024-12-20T10:30:45.000000Z"),
     *                         @OA\Property(property="account", type="object",
     *                             @OA\Property(property="id", type="integer", example=1),
     *                             @OA\Property(property="username", type="string", example="john_doe"),
     *                             @OA\Property(property="full_name", type="string", example="John Doe")
     *                         )
     *                     )
     *                 ),
     *                 @OA\Property(property="pagination", type="object",
     *                     @OA\Property(property="total", type="integer", example=100),
     *                     @OA\Property(property="per_page", type="integer", example=15),
     *                     @OA\Property(property="current_page", type="integer", example=1),
     *                     @OA\Property(property="last_page", type="integer", example=7)
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function index(Request $request)
    {
        $query = Thread::with(['account:id,username,full_name,avatar'])
            ->withCount('replies')
            ->select(['id', 'title', 'slug', 'content', 'account_id', 'views', 'created_at', 'updated_at']);

        // Search functionality
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = $request->search;
            $query->where(function($q) use ($searchTerm) {
                $q->where('title', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('content', 'LIKE', "%{$searchTerm}%");
            });
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        $allowedSorts = ['created_at', 'updated_at', 'views', 'title'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        // Get total count before pagination
        $total = $query->count();
        
        // Pagination parameters
        $page = $request->get('page', 1);
        $perPage = min($request->get('per_page', 15), 50); // Max 50 per page
        $offset = ($page - 1) * $perPage;
        
        // Get threads with manual pagination
        $threads = $query->offset($offset)->limit($perPage)->get();

        return response()->json([
            'success' => true,
            'data' => $threads,
            'pagination' => [
                'current_page' => (int) $page,
                'per_page' => (int) $perPage,
                'total' => $total,
                'last_page' => ceil($total / $perPage),
                'has_more' => ($offset + $perPage) < $total,
            ]
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/app/forum/{slug}",
     *     tags={"Forum"},
     *     summary="Xem chi tiết thread",
     *     description="Xem chi tiết thread theo slug với danh sách replies gốc (parent_id = 0 hoặc null)",
     *     @OA\Parameter(
     *         name="slug",
     *         in="path",
     *         description="Slug của thread",
     *         required=true,
     *         @OA\Schema(type="string", example="thao-luan-ve-moi-truong-abc123")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Lấy chi tiết thread thành công"),
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="thread", type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="title", type="string", example="Thảo luận về môi trường"),
     *                     @OA\Property(property="slug", type="string", example="thao-luan-ve-moi-truong-abc123"),
     *                     @OA\Property(property="content", type="string", example="Nội dung bài viết chi tiết về môi trường..."),
     *                     @OA\Property(property="views", type="integer", example=151),
     *                     @OA\Property(property="created_at", type="string", example="2024-12-20T10:30:45.000000Z"),
     *                     @OA\Property(property="updated_at", type="string", example="2024-12-20T10:30:45.000000Z"),
     *                     @OA\Property(property="children_count", type="integer", example=0),
     *                     @OA\Property(property="account", type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="username", type="string", example="john_doe"),
     *                         @OA\Property(property="full_name", type="string", example="John Doe"),
     *                         @OA\Property(property="avatar", type="string", nullable=true, example="http://example.com/avatar.jpg")
     *                     )
     *                 ),
     *                 @OA\Property(property="replies", type="array",
     *                     @OA\Items(
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="content", type="string", example="Bình luận gốc về chủ đề này"),
     *                         @OA\Property(property="thread_id", type="integer", example=1),
     *                         @OA\Property(property="parent_id", type="integer", nullable=true, example=null),
     *                         @OA\Property(property="created_at", type="string", example="2024-12-20T11:00:00.000000Z"),
     *                         @OA\Property(property="updated_at", type="string", example="2024-12-20T11:00:00.000000Z"),
     *                         @OA\Property(property="children_count", type="integer", example=0),
     *                         @OA\Property(property="account", type="object",
     *                             @OA\Property(property="id", type="integer", example=2),
     *                             @OA\Property(property="username", type="string", example="jane_doe"),
     *                             @OA\Property(property="full_name", type="string", example="Jane Doe"),
     *                             @OA\Property(property="avatar", type="string", nullable=true, example="http://example.com/avatar2.jpg")
     *                         )
     *                     )
     *                 ),
     *                 @OA\Property(property="stats", type="object",
     *                     @OA\Property(property="total_replies", type="integer", example=8),
     *                     @OA\Property(property="total_views", type="integer", example=151),
     *                     @OA\Property(property="created_date", type="string", example="2024-12-20"),
     *                     @OA\Property(property="last_activity", type="string", example="2024-12-20T11:30:00.000000Z")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Thread không tồn tại",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Thread không tồn tại hoặc đã bị xóa"),
     *             @OA\Property(property="data", type="null", example=null)
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Lỗi server",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Có lỗi xảy ra khi tải thread"),
     *             @OA\Property(property="data", type="null", example=null)
     *         )
     *     )
     * )
     */
    public function show($slug)
    {
        try {
            // Load thread with account relationship
            $thread = Thread::with([
                'account:id,username,full_name,avatar',
            ])->where('slug', $slug)->first();

            if (!$thread) {
                return response()->json([
                    'success' => false,
                    'message' => 'Thread không tồn tại hoặc đã bị xóa!',
                    'data' => null
                ], 404);
            }

            // Increment view count
            $thread->increment('views');

            // Lấy replies gốc (parent_id = 0 hoặc null), sort mới nhất
            $replies = Reply::with(['account:id,username,full_name,avatar'])
                ->withCount('children')
                ->where('thread_id', $thread->id)
                ->where(function($q) {
                    $q->where('parent_id', 0)
                      ->orWhereNull('parent_id');
                })
                ->orderByDesc('created_at')
                ->get();

            // Prepare thread data
            $threadData = [
                'id' => $thread->id,
                'title' => $thread->title,
                'slug' => $thread->slug,
                'content' => $thread->content,
                'views' => $thread->views,
                'created_at' => formatDateTime($thread->created_at),
                'updated_at' => formatDateTime($thread->updated_at),
                'account' => [
                    'id' => $thread->account->id,
                    'username' => $thread->account->username,
                    'full_name' => $thread->account->full_name ?? '',
                    'avatar' => $thread->account->avatar ? url($thread->account->avatar) : null,
                ]
            ];

            // Format replies
            $repliesFormatted = $replies->map(function($reply) {
                return [
                    'id' => $reply->id,
                    'content' => $reply->content,
                    'thread_id' => $reply->thread_id,
                    'parent_id' => $reply->parent_id,
                    'children_count' => $reply->children_count,
                    'created_at' => formatDateTime($reply->created_at),
                    'updated_at' => formatDateTime($reply->updated_at),
                    'account' => [
                        'id' => $reply->account->id,
                        'username' => $reply->account->username,
                        'full_name' => $reply->account->full_name ?? '',
                        'avatar' => $reply->account->avatar ? url($reply->account->avatar) : null,
                    ]
                ];
            });

            // Prepare statistics
            $stats = [
                'total_replies' => $replies->count(),
                'total_views' => $thread->views,
                'created_date' => $thread->created_at ? $thread->created_at->format('Y-m-d') : null,
                'last_activity' => $replies->first()?->created_at ? formatDateTime($replies->first()->created_at) : ($thread->updated_at ? formatDateTime($thread->updated_at) : null)
            ];

            return response()->json([
                'success' => true,
                'message' => 'Lấy chi tiết thread thành công',
                'data' => [
                    'thread' => $threadData,
                    'replies' => $repliesFormatted,
                    'stats' => $stats
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Forum show error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tải thread. Vui lòng thử lại sau!',
                'data' => null
            ], 500);
        }
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string|min:10',
        ]);
        
        $user = $request->user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => 'Bạn cần đăng nhập!'], 401);
        }
        $accountId = $user->id;
        
        $thread = Thread::create([
            'title' => $request->input('title'),
            'slug' => Str::slug($request->input('title')) . '-' . Str::random(5),
            'content' => $request->input('content'),
            'account_id' => $accountId,
            'views' => 0,
        ]);

        // Load with relationships for response
        $thread->load('account:id,username,full_name,avatar');
        
        return response()->json([
            'success' => true,
            'message' => 'Đăng chủ đề thành công!',
            'data' => $thread
        ], 201);
    }

    /**
     * @OA\Put(
     *     path="/api/app/forum/{slug}",
     *     tags={"Forum"},
     *     summary="Cập nhật thread",
     *     description="Cập nhật thread (chỉ tác giả)",
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="slug",
     *         in="path",
     *         description="Slug của thread",
     *         required=true,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="title", type="string", example="Tiêu đề đã cập nhật"),
     *             @OA\Property(property="content", type="string", example="Nội dung đã được chỉnh sửa")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Cập nhật thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Thread đã được cập nhật")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Không có quyền",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Bạn không có quyền chỉnh sửa thread này")
     *         )
     *     )
     * )
     */
    public function update(Request $request, $slug)
    {
        $request->validate([
            'title' => 'sometimes|string|max:255',
            'content' => 'sometimes|string|min:10',
        ]);
        
        $user = $request->user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => 'Bạn cần đăng nhập!'], 401);
        }
        $accountId = $user->id;
        
        $thread = Thread::where('slug', $slug)->first();
        if (!$thread) {
            return response()->json(['success' => false, 'message' => 'Thread không tồn tại!'], 404);
        }
        
        // Check if user is the author
        if ($thread->account_id !== $accountId) {
            return response()->json(['success' => false, 'message' => 'Bạn không có quyền chỉnh sửa thread này!'], 403);
        }
        
        // Update fields
        if ($request->filled('title')) {
            $thread->title = $request->input('title');
            $thread->slug = Str::slug($request->input('title')) . '-' . Str::random(5);
        }
        if ($request->filled('content')) {
            $thread->content = $request->input('content');
        }
        
        $thread->save();
        $thread->load('account:id,username,full_name,avatar');
        
        return response()->json([
            'success' => true, 
            'message' => 'Cập nhật thread thành công!',
            'data' => $thread
        ]);
    }

    /**
     * @OA\Delete(
     *     path="/api/app/forum/{slug}",
     *     tags={"Forum"},
     *     summary="Xóa thread",
     *     description="Xóa thread (chỉ tác giả)",
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="slug",
     *         in="path",
     *         description="Slug của thread",
     *         required=true,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Xóa thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Thread đã được xóa")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Không có quyền",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Bạn không có quyền xóa thread này")
     *         )
     *     )
     * )
     */
    public function destroy(Request $request, $slug)
    {
        $user = $request->user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => 'Bạn cần đăng nhập!'], 401);
        }
        $accountId = $user->id;
        
        $thread = Thread::where('slug', $slug)->first();
        if (!$thread) {
            return response()->json(['success' => false, 'message' => 'Thread không tồn tại!'], 404);
        }
        
        // Check if user is the author
        if ($thread->account_id !== $accountId) {
            return response()->json(['success' => false, 'message' => 'Bạn không có quyền xóa thread này!'], 403);
        }
        
        $thread->delete();
        
        return response()->json([
            'success' => true, 
            'message' => 'Xóa thread thành công!'
        ]);
    }

    /**
     * @OA\Post(
     *     path="/api/app/forum/reply",
     *     tags={"Forum"},
     *     summary="Trả lời thread",
     *     description="Tạo reply cho thread",
     *     security={{"sanctum": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"thread_id", "content"},
     *             @OA\Property(property="thread_id", type="integer", example=1),
     *             @OA\Property(property="content", type="string", example="Phản hồi của tôi cho chủ đề này"),
     *             @OA\Property(property="parent_id", type="integer", nullable=true, example=null, description="ID reply cha (cho nested reply)")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Trả lời thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Trả lời thành công"),
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="reply", type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="content", type="string", example="Phản hồi của tôi..."),
     *                     @OA\Property(property="thread_id", type="integer", example=1),
     *                     @OA\Property(property="parent_id", type="integer", nullable=true, example=null)
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Chưa đăng nhập",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Vui lòng đăng nhập")
     *         )
     *     )
     * )
     */
    public function reply(Request $request)
    {
        $request->validate([
            'thread_id' => 'required|exists:threads,id',
            'content' => 'required|string',
            'parent_id' => 'nullable|exists:replies,id',
        ]);
        
        $user = $request->user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => 'Bạn cần đăng nhập!'], 401);
        }
        $accountId = $user->id;
        
        $reply = Reply::create([
            'thread_id' => $request->input('thread_id'),
            'account_id' => $accountId,
            'parent_id' => $request->input('parent_id'),
            'content' => $request->input('content'),
        ]);
        
        $reply->load('account:id,username,full_name,avatar');
        
        return response()->json([
            'success' => true, 
            'message' => 'Gửi phản hồi thành công!',
            'data' => $reply
        ], 201);
    }

    /**
     * @OA\Put(
     *     path="/api/app/forum/reply/{replyId}",
     *     tags={"Forum"},
     *     summary="Cập nhật reply",
     *     description="Cập nhật reply (chỉ tác giả)",
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="replyId",
     *         in="path",
     *         description="ID của reply",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="content", type="string", example="Nội dung đã được chỉnh sửa")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Cập nhật thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Reply đã được cập nhật")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Không có quyền",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Bạn không có quyền chỉnh sửa reply này")
     *         )
     *     )
     * )
     */
    public function updateReply(Request $request, $replyId)
    {
        $request->validate([
            'content' => 'required|string|min:5',
        ]);
        
        $user = $request->user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => 'Bạn cần đăng nhập!'], 401);
        }
        $accountId = $user->id;
        
        $reply = Reply::find($replyId);
        if (!$reply) {
            return response()->json(['success' => false, 'message' => 'Reply không tồn tại!'], 404);
        }
        
        // Check if user is the author
        if ($reply->account_id !== $accountId) {
            return response()->json(['success' => false, 'message' => 'Bạn không có quyền chỉnh sửa reply này!'], 403);
        }
        
        $reply->content = $request->input('content');
        $reply->save();
        
        $reply->load('account:id,username,full_name,avatar');
        
        return response()->json([
            'success' => true, 
            'message' => 'Cập nhật reply thành công!',
            'data' => $reply
        ]);
    }

    /**
     * @OA\Delete(
     *     path="/api/app/forum/reply/{replyId}",
     *     tags={"Forum"},
     *     summary="Xóa reply",
     *     description="Xóa reply (chỉ tác giả)",
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="replyId",
     *         in="path",
     *         description="ID của reply",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Xóa thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Reply đã được xóa")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Không có quyền",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Bạn không có quyền xóa reply này")
     *         )
     *     )
     * )
     */
    public function destroyReply(Request $request, $replyId)
    {
        $user = $request->user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => 'Bạn cần đăng nhập!'], 401);
        }
        $accountId = $user->id;
        
        $reply = Reply::find($replyId);
        if (!$reply) {
            return response()->json(['success' => false, 'message' => 'Reply không tồn tại!'], 404);
        }
        
        // Check if user is the author
        if ($reply->account_id !== $accountId) {
            return response()->json(['success' => false, 'message' => 'Bạn không có quyền xóa reply này!'], 403);
        }
        
        $reply->delete();
        
        return response()->json([
            'success' => true, 
            'message' => 'Xóa reply thành công!'
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/app/forum/user/threads",
     *     tags={"Forum"},
     *     summary="Threads của user",
     *     description="Lấy danh sách threads của user hiện tại",
     *     security={{"sanctum": {}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Số trang",
     *         required=false,
     *         @OA\Schema(type="integer", default=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="threads", type="array",
     *                     @OA\Items(
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="title", type="string", example="Thread của tôi"),
     *                         @OA\Property(property="replies_count", type="integer", example=5),
     *                         @OA\Property(property="views", type="integer", example=150)
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Chưa đăng nhập",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Vui lòng đăng nhập")
     *         )
     *     )
     * )
     */
    public function userThreads(Request $request)
    {
        $user = $request->user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => 'Bạn cần đăng nhập!'], 401);
        }
        $accountId = $user->id;

        $query = Thread::with(['account:id,username,full_name,avatar'])
            ->withCount('replies')
            ->where('account_id', $accountId)
            ->orderBy('created_at', 'desc');

        // Get total count before pagination
        $total = $query->count();
        
        // Pagination parameters
        $page = $request->get('page', 1);
        $perPage = min($request->get('per_page', 15), 50);
        $offset = ($page - 1) * $perPage;
        
        // Get threads with manual pagination
        $threads = $query->offset($offset)->limit($perPage)->get();

        return response()->json([
            'success' => true,
            'data' => $threads,
            'pagination' => [
                'current_page' => (int) $page,
                'per_page' => (int) $perPage,
                'total' => $total,
                'last_page' => ceil($total / $perPage),
                'has_more' => ($offset + $perPage) < $total,
            ]
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/app/forum/{slug}/replies",
     *     tags={"Forum"},
     *     summary="Lấy các bình luận con (reply) của một comment gốc",
     *     description="Lấy danh sách reply con theo parent_id trong một thread",
     *     @OA\Parameter(
     *         name="slug",
     *         in="path",
     *         required=true,
     *         description="Slug của thread",
     *         @OA\Schema(type="string", example="thao-luan-ve-moi-truong-abc123")
     *     ),
     *     @OA\Parameter(
     *         name="parent_id",
     *         in="query",
     *         required=true,
     *         description="ID của comment gốc (parent)",
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="array",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer", example=2),
     *                     @OA\Property(property="content", type="string", example="Phản hồi cho comment gốc"),
     *                     @OA\Property(property="thread_id", type="integer", example=1),
     *                     @OA\Property(property="parent_id", type="integer", example=1),
     *                     @OA\Property(property="created_at", type="string", example="2024-12-20T11:30:00.000000Z"),
     *                     @OA\Property(property="updated_at", type="string", example="2024-12-20T11:30:00.000000Z"),
     *                     @OA\Property(property="account", type="object",
     *                         @OA\Property(property="id", type="integer", example=3),
     *                         @OA\Property(property="username", type="string", example="user123"),
     *                         @OA\Property(property="full_name", type="string", example="User Name"),
     *                         @OA\Property(property="avatar", type="string", nullable=true, example=null)
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Thiếu parent_id",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Thiếu parent_id")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Thread không tồn tại",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Thread không tồn tại hoặc đã bị xóa")
     *         )
     *     )
     * )
     */
    public function childReplies($slug, Request $request)
    {
        $parentId = $request->query('parent_id');
        if (!$parentId) {
            return response()->json([
                'success' => false,
                'message' => 'Thiếu parent_id'
            ], 400);
        }

        $thread = Thread::where('slug', $slug)->first();
        if (!$thread) {
            return response()->json([
                'success' => false,
                'message' => 'Thread không tồn tại hoặc đã bị xóa!'
            ], 404);
        }

        $replies = Reply::with(['account:id,username,full_name,avatar'])
            ->where('thread_id', $thread->id)
            ->where('parent_id', $parentId)
            ->orderBy('created_at', 'asc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $replies
        ]);
    }

    /**
     * Helper: Organize replies into nested structure
     */
    private function organizeReplies($replies)
    {
        $organized = [];
        $replyMap = [];

        // First pass: create a map of all replies
        foreach ($replies as $reply) {
            $replyMap[$reply->id] = $reply->toArray();
            $replyMap[$reply->id]['children'] = [];
        }

        // Second pass: organize into parent-child structure
        foreach ($replyMap as $id => $reply) {
            if ($reply['parent_id'] === null) {
                $organized[] = &$replyMap[$id];
            } else {
                if (isset($replyMap[$reply['parent_id']])) {
                    $replyMap[$reply['parent_id']]['children'][] = &$replyMap[$id];
                }
            }
        }

        return $organized;
    }

    /**
     * Helper: Delete reply and all its children recursively
     */
    private function deleteReplyAndChildren(Reply $reply)
    {
        // Find and delete all children first
        $children = Reply::where('parent_id', $reply->id)->get();
        foreach ($children as $child) {
            $this->deleteReplyAndChildren($child);
        }

        // Delete the reply itself
        $reply->delete();
    }


    /**
     * @OA\Post(
     *     path="/api/app/forum/create/threads",
     *     tags={"Forum"},
     *     summary="Tạo thread mới (API riêng)",
     *     description="Tạo mới một thread, yêu cầu đăng nhập (sanctum)",
     *     security={{"sanctum": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"title", "content"},
     *             @OA\Property(property="title", type="string", example="Tiêu đề thread mới"),
     *             @OA\Property(property="content", type="string", example="Nội dung thread tối thiểu 10 ký tự")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Tạo thread thành công",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Tạo thread thành công!"),
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="title", type="string", example="Tiêu đề thread mới"),
     *                 @OA\Property(property="slug", type="string", example="tieu-de-thread-moi-abc12"),
     *                 @OA\Property(property="content", type="string", example="Nội dung thread"),
     *                 @OA\Property(property="account_id", type="integer", example=2),
     *                 @OA\Property(property="views", type="integer", example=0),
     *                 @OA\Property(property="created_at", type="string", example="2024-12-20T10:30:45.000000Z"),
     *                 @OA\Property(property="account", type="object",
     *                     @OA\Property(property="id", type="integer", example=2),
     *                     @OA\Property(property="username", type="string", example="user123"),
     *                     @OA\Property(property="full_name", type="string", example="Nguyễn Văn A"),
     *                     @OA\Property(property="avatar", type="string", nullable=true, example=null)
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Chưa đăng nhập",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="message", type="string", example="Bạn cần đăng nhập!")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Lỗi validate",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="The given data was invalid."),
     *             @OA\Property(property="errors", type="object")
     *         )
     *     )
     * )
     */
    public function createThreads(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string|min:10',
        ]);
        $user = $request->user();
        if (!$user) {
            return response()->json(['success' => false, 'message' => 'Bạn cần đăng nhập!'], 401);
        }
        $accountId = $user->id;
        $thread = Thread::create([
            'title' => $request->input('title'),
            'slug' => Str::slug($request->input('title')) . '-' . Str::random(5),
            'content' => $request->input('content'),
            'account_id' => $accountId,
            'views' => 0,
        ]);
        $thread->load('account:id,username,full_name,avatar');
        return response()->json([
            'success' => true,
            'message' => 'Tạo thread thành công!',
            'data' => $thread
        ], 201);
    }
} 