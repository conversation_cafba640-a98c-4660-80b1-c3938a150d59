<?php $__env->startSection('title', $event->title); ?>
<?php $__env->startSection('header', $event->title); ?>
<?php $__env->startSection('content'); ?>
    <div class="container mx-auto py-8 max-w-3xl">
        <div class="bg-white rounded-2xl shadow-2xl p-6 border border-[#10B981]/20">
            <?php
                $defaultImage = '/images/default-event.svg';
                $imageUrl = $event->featured_image ? asset('storage/' . $event->featured_image) : $defaultImage;
            ?>
            <img src="<?php echo e($imageUrl); ?>" alt="<?php echo e($event->title); ?>"
                class="rounded mb-4 w-full h-64 object-cover border border-[#10B981]/30"
                onerror="this.src='<?php echo e($defaultImage); ?>'; this.onerror=null;" loading="lazy">
            <h1 class="text-3xl font-bold bg-gradient-to-r from-[#10B981] to-[#0EA5E9] bg-clip-text text-transparent mb-2">
                <?php echo e($event->title); ?>

            </h1>
            <div class="text-sm text-gray-500 mb-2">
                <span class="font-medium text-[#10B981]"><?php echo e(trans_db('time.thoi_gian')); ?></span>
                <?php echo e(\Carbon\Carbon::parse($event->start_date)->format('d/m/Y H:i')); ?>

                <?php if($event->end_date): ?>
                    - <?php echo e(\Carbon\Carbon::parse($event->end_date)->format('d/m/Y H:i')); ?>

                <?php endif; ?>
            </div>
            <div class="text-sm text-gray-500 mb-2">
                <span class="font-medium text-[#0EA5E9]"><?php echo e(trans_db('general.dia_diem')); ?></span> <?php echo e($event->location); ?>

            </div>
            <?php if($event->max_participants): ?>
                <div class="text-xs text-gray-400 mb-2">
                    <span class="font-medium"><?php echo e(trans_db('general.so_nguoi_toi_da')); ?>:</span> <?php echo e($event->max_participants); ?>

                </div>
            <?php endif; ?>
            <div class="text-gray-700 mb-4">
                <strong><?php echo e(trans_db('general.mo_ta')); ?>:</strong> <?php echo e($event->description); ?>

            </div>
            <div class="prose max-w-none mb-6">
                <?php echo nl2br(e($event->content)); ?>

            </div>
            <?php
                $now = \Carbon\Carbon::now();
                $start = \Carbon\Carbon::parse($event->start_date);
                $end = $event->end_date ? \Carbon\Carbon::parse($event->end_date) : null;
                if ($now->lt($start)) {
                    $status = 1; // Chưa đến
                } elseif ($end && $now->gt($end)) {
                    $status = 2; // Đã kết thúc
                } else {
                    $status = 3; // Đang diễn ra
                } 
            ?>
            <?php if($status == 1): ?>
                <div
                    class="text-sm text-gray-500 mb-2 w-full py-3 rounded-lg text-white font-semibold transition bg-gradient-to-r from-[#10B981] to-[#0EA5E9] hover:from-[#0EA5E9] hover:to-[#10B981] flex justify-center items-center">
                    <span class="font-medium text-yellow-500"><?php echo e(trans_db('general.sap_dien_ra')); ?></span>
                </div>
            <?php elseif($status == 2): ?>
                <div class="text-sm text-gray-500 mb-2">
                    <span class="font-medium text-gray-500"><?php echo e(trans_db('general.ket_thuc')); ?></span>
                </div>
            <?php else: ?>
                <?php if(auth()->guard()->check()): ?>
                    <!-- Hiển thị nút đăng ký cho người đã đăng nhập -->
                    <button id="register-btn" data-id="<?php echo e($event->id); ?>"
                        class="w-full py-3 rounded-lg text-white font-semibold transition <?php echo e($registered ? 'bg-gray-400 cursor-not-allowed' : 'bg-gradient-to-r from-[#10B981] to-[#0EA5E9] hover:from-[#0EA5E9] hover:to-[#10B981]'); ?>"
                        <?php if($registered): ?> disabled <?php endif; ?>>
                        <?php echo e($registered ? trans_db('nav.da_dang_ky_tham_gia') : trans_db('general.tham_gia_su_kien')); ?>

                    </button>
                <?php else: ?>
                    <!-- Hiển thị QR code cho người chưa đăng nhập -->
                    <div class="text-center">
                        <div class="bg-gradient-to-r from-[#10B981] to-[#0EA5E9] p-6 rounded-xl mb-6 shadow-lg">
                            <h3 class="text-white font-bold text-lg mb-4"><?php echo e(trans_db('general.quet_ma_qr_de_tham_gia')); ?></h3>
                            <div class="bg-white p-6 rounded-xl inline-block shadow-inner">
                                <div id="qrcode-container" class="flex justify-center items-center min-h-[200px]">
                                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-[#10B981]"></div>
                                </div>
                            </div>
                            <p class="text-white/90 text-sm mt-4 font-medium">
                                <?php echo e(trans_db('general.quet_ma_qr_bang_dien_thoai_de_mo_trang_su_kien')); ?>

                            </p>
                        </div>
                    </div>
                    <!-- Thông tin bổ sung và nút copy -->
                    <div class="bg-gray-50 p-4 rounded-lg mb-4">
                        <div class="flex items-center justify-center space-x-2 text-gray-600 mb-3">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-sm"><?php echo e(trans_db('general.hoac_dang_nhap_de_tham_gia_truc_tiep')); ?></span>
                        </div>

                        <!-- Nút copy URL -->
                        <button id="copy-url-btn"
                            class="w-full py-2 px-4 bg-gray-200 hover:bg-gray-300 text-gray-700 text-sm rounded-lg transition-all duration-200 flex items-center justify-center space-x-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z">
                                </path>
                            </svg>
                            <span><?php echo e(trans_db('general.sao_chep_lien_ket')); ?></span>
                        </button>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>

    <?php if(auth()->guard()->guest()): ?>
        <!-- Thêm thư viện QR code cho người chưa đăng nhập -->
        <script>
            // Load QRCode library dynamically
            function loadQRCodeLibrary() {
                return new Promise((resolve, reject) => {
                    if (typeof QRious !== 'undefined') {
                        resolve();
                        return;
                    }

                    const script = document.createElement('script');
                    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/qrious/4.0.2/qrious.min.js';
                    script.onload = () => {
                        console.log('QRious library loaded successfully');
                        resolve();
                    };
                    script.onerror = () => {
                        console.error('Failed to load QRious library');
                        reject(new Error('Failed to load QRious library'));
                    };
                    document.head.appendChild(script);
                });
            }

            // Đợi DOM load xong
            document.addEventListener('DOMContentLoaded', function () {
                async function initQRCode() {
                    try {
                        await loadQRCodeLibrary();
                    } catch (error) {
                        console.error('Error loading QRCode library:', error);
                        const qrContainer = document.getElementById('qrcode-container');
                        if (qrContainer) {
                            qrContainer.innerHTML = `
                                                <div class="text-center p-4">
                                                    <p class="text-red-500 text-sm">Không thể tải thư viện QR Code</p>
                                                    <p class="text-gray-500 text-xs mt-1">Vui lòng thử lại sau</p>
                                                </div>
                                            `;
                        }
                        return;
                    }

                    // Tạo URL đầy đủ cho sự kiện với slug
                    const eventSlug = "<?php echo e($event->slug ?? $event->id); ?>";
                    const eventUrl = eventSlug;

                    // Hiển thị URL trong console để debug
                    console.log('QR Code URL:', eventUrl);

                    const qrContainer = document.getElementById('qrcode-container');
                    if (!qrContainer) {
                        console.error('QR container not found');
                        return;
                    }

                    // Tạo QR code với QRious
                    try {
                        const canvas = document.createElement('canvas');
                        const qr = new QRious({
                            element: canvas,
                            value: eventUrl,
                            size: 200,
                            level: 'M',
                            foreground: '#1F2937',
                            background: '#FFFFFF'
                        });

                        // Xóa loading spinner và thêm canvas
                        qrContainer.innerHTML = '';
                        qrContainer.appendChild(canvas);

                        // Thêm style cho canvas
                        canvas.style.borderRadius = '8px';
                        canvas.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
                        canvas.style.opacity = '0';

                        // Thêm hiệu ứng fade in
                        setTimeout(() => {
                            canvas.style.transition = 'opacity 0.5s';
                            canvas.style.opacity = '1';
                        }, 100);

                        console.log('QR Code created successfully');
                    } catch (error) {
                        console.error('Lỗi tạo QR code:', error);
                        qrContainer.innerHTML = `
                                            <div class="text-center p-4">
                                                <svg class="w-12 h-12 text-red-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                                </svg>
                                                <p class="text-red-500 text-sm"><?php echo e(trans_db("general.khong_the_tao_ma_qr")); ?></p>
                                                <p class="text-gray-500 text-xs mt-1">Vui lòng thử lại sau</p>
                                            </div>
                                        `;
                    }

                    // Thêm chức năng copy URL
                    const copyBtn = document.getElementById('copy-url-btn');
                    if (copyBtn) {
                        copyBtn.addEventListener('click', function () {
                            navigator.clipboard.writeText(eventUrl).then(function () {
                                // Hiển thị thông báo đã copy
                                const span = copyBtn.querySelector('span');
                                const originalText = span.textContent;
                                span.textContent = '<?php echo e(trans_db("general.da_sao_chep")); ?>';
                                copyBtn.classList.remove('bg-gray-200', 'text-gray-700');
                                copyBtn.classList.add('bg-green-500', 'text-white');

                                setTimeout(function () {
                                    span.textContent = originalText;
                                    copyBtn.classList.remove('bg-green-500', 'text-white');
                                    copyBtn.classList.add('bg-gray-200', 'text-gray-700');
                                }, 2000);
                            }).catch(function () {
                                alert('Không thể sao chép URL');
                            });
                        });
                    }
                }

                // Khởi tạo QR code
                initQRCode();
            });
        </script>
    <?php endif; ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\PROJECT\backend-app_don_rac\resources\views/pages/event-show.blade.php ENDPATH**/ ?>