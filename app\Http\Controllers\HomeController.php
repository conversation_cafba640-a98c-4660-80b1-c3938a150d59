<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Post;
use App\Models\Category;
use App\Models\Event;
use App\Models\Testimonial;

class HomeController extends Controller
{
    /**
     * Display the homepage.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // Get latest posts for homepage
        $latestPosts = Post::with('category')
            ->latest()
            ->take(6)
            ->get();

        // Get all categories
        $categories = Category::withCount('posts')
            ->orderBy('posts_count', 'desc')
            ->take(10)
            ->get();

        // Get 3 ongoing events for Communities section
        $ongoingEvents = Event::where('status', 'approved')
            ->where(function ($q) {
                $q->whereNull('end_date')->orWhere('end_date', '>=', now());
            })
            ->orderBy('start_date', 'desc')
            ->take(3)
            ->get();

        // Get 3 active testimonials for Experts section
        $testimonials = Testimonial::active()
            ->ordered()
            ->take(3)
            ->get();

        return view('home', compact('latestPosts', 'categories', 'ongoingEvents', 'testimonials'));
    }
}
